tokenizers-0.21.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
tokenizers-0.21.1.dist-info/METADATA,sha256=GL78TpkKOH8KBbB1Bba01wXgp9ImYCYPAtmQl3J4dZk,6774
tokenizers-0.21.1.dist-info/RECORD,,
tokenizers-0.21.1.dist-info/WHEEL,sha256=uj4_7UkXZf4ot0J6KHbl5KUFIRWFef7Mj2F84w94bdk,102
tokenizers/__init__.py,sha256=ZE5ZagUvobBScrHBQdEobhx4wqM0bsq9F9aLYkBNjYQ,2615
tokenizers/__init__.pyi,sha256=jw34WZXaYu8NBBJ2_cypfOqJYxI7CXKPzlveisXw4XQ,40182
tokenizers/__pycache__/__init__.cpython-39.pyc,,
tokenizers/decoders/__init__.py,sha256=hfwM6CFUDvlMGGL4-xsaaYz81K9P5rQI5ZL5UHWK8Y4,372
tokenizers/decoders/__init__.pyi,sha256=qylMinPKn2_NAMC6BzcXqFyndoicLws34Q6OLH1gcp4,7378
tokenizers/decoders/__pycache__/__init__.cpython-39.pyc,,
tokenizers/implementations/__init__.py,sha256=VzAsplaIo7rl4AFO8Miu7ig7MfZjvonwVblZw01zR6M,310
tokenizers/implementations/__pycache__/__init__.cpython-39.pyc,,
tokenizers/implementations/__pycache__/base_tokenizer.cpython-39.pyc,,
tokenizers/implementations/__pycache__/bert_wordpiece.cpython-39.pyc,,
tokenizers/implementations/__pycache__/byte_level_bpe.cpython-39.pyc,,
tokenizers/implementations/__pycache__/char_level_bpe.cpython-39.pyc,,
tokenizers/implementations/__pycache__/sentencepiece_bpe.cpython-39.pyc,,
tokenizers/implementations/__pycache__/sentencepiece_unigram.cpython-39.pyc,,
tokenizers/implementations/base_tokenizer.py,sha256=2TFZhLupaJiMDYGJuUNmxYJv-cnR8bDHmbMzaYpFROs,14206
tokenizers/implementations/bert_wordpiece.py,sha256=sKCum0FKPYdSgJFJN8LDerVBoTDRSqyqSdrcm-lvQqI,5520
tokenizers/implementations/byte_level_bpe.py,sha256=OA_jyy3EQmYTa6hnf-EKwLOFuyroqFYOJz25ysM2BUk,4289
tokenizers/implementations/char_level_bpe.py,sha256=Q2ZEAW0xMQHF7YCUtmplwaxbU-J0P2NK4PJGMxUb-_c,5466
tokenizers/implementations/sentencepiece_bpe.py,sha256=LwrofoohnUfME2lK2lQYoyQIhP84RP0CIlHRaj0hyNs,3738
tokenizers/implementations/sentencepiece_unigram.py,sha256=SYiVXL8ZtqLXKpuqwnwmrfxgGotu8yAkOu7dLztEXIo,7580
tokenizers/models/__init__.py,sha256=eJZ4HTAQZpxnKILNylWaTFqxXy-Ba6OKswWN47feeV8,176
tokenizers/models/__init__.pyi,sha256=clPTwiyjz7FlVdEuwo_3Wa_TmQrbZhW0SGmnNylepnY,16929
tokenizers/models/__pycache__/__init__.cpython-39.pyc,,
tokenizers/normalizers/__init__.py,sha256=_06w4cqRItveEgIddYaLMScgkSOkIAMIzYCesb5AA4U,841
tokenizers/normalizers/__init__.pyi,sha256=lSFqDb_lPZBfRxEG99EcFEaU1HlnIhIQUu7zZIyP4AY,20898
tokenizers/normalizers/__pycache__/__init__.cpython-39.pyc,,
tokenizers/pre_tokenizers/__init__.py,sha256=wd6KYQA_RsGSQK-HeG9opTRhv4ttSRkyno2dk6az-PM,557
tokenizers/pre_tokenizers/__init__.pyi,sha256=k9Jnez-t4ww_Pyj8OkVeo78v9TntcNn6ri9kwB590KE,23606
tokenizers/pre_tokenizers/__pycache__/__init__.cpython-39.pyc,,
tokenizers/processors/__init__.py,sha256=xM2DEKwKtHIumHsszM8AMkq-AlaqvBZFXWgLU8SNhOY,307
tokenizers/processors/__init__.pyi,sha256=hx767ZY8SHhxb_hiXPRxm-f_KcoR4XDx7vfK2c0lR-Q,11357
tokenizers/processors/__pycache__/__init__.cpython-39.pyc,,
tokenizers/tokenizers.abi3.so,sha256=ufU2vGIws7JKA2fTmhDAAmfd1XMyqtW2cRk7s0d8PYc,7593568
tokenizers/tools/__init__.py,sha256=xG8caB9OHC8cbB01S5vYV14HZxhO6eWbLehsb70ppio,55
tokenizers/tools/__pycache__/__init__.cpython-39.pyc,,
tokenizers/tools/__pycache__/visualizer.cpython-39.pyc,,
tokenizers/tools/visualizer-styles.css,sha256=zAydq1oGWD8QEll4-eyL8Llw0B1sty_hpIE3tYxL02k,4850
tokenizers/tools/visualizer.py,sha256=0W90s4Qm8Nd6P-npqQX-bCMLQCfAEPk2qgj-K8r7OMc,14624
tokenizers/trainers/__init__.py,sha256=UTu22AGcp76IvpW45xLRbJWET04NxPW6NfCb2YYz0EM,248
tokenizers/trainers/__init__.pyi,sha256=3TwFKts4me7zQfVRcSTmtXYiP4XwcRjfAYtwqoZVtoQ,5382
tokenizers/trainers/__pycache__/__init__.cpython-39.pyc,,
