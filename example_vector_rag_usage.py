#!/usr/bin/env python3
"""
Example usage of Vector RAG Enhanced Pipeline.

This script demonstrates how to use the Vector RAG enhanced pipeline
with your existing power plant extraction system.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.vector_rag_pipeline import VectorRAGPowerPlantPipeline, extract_with_vector_rag


def setup_logging():
    """Configure logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('vector_rag_example.log'),
            logging.StreamHandler()
        ]
    )


async def example_basic_usage():
    """Example 1: Basic Vector RAG enhanced extraction."""
    print("\n📋 Example 1: Basic Vector RAG Enhanced Extraction")
    print("=" * 60)
    
    plant_name = "Jhajjar Power Plant"
    
    try:
        # Use the convenience function
        org_details, plant_details, extraction_info = await extract_with_vector_rag(
            plant_name=plant_name,
            enable_rag=True
        )
        
        print(f"✅ Extraction completed for {plant_name}")
        print(f"📊 Strategy: {extraction_info.get('strategy', 'unknown')}")
        print(f"🧠 Vector RAG enabled: {extraction_info.get('vector_rag_enabled', False)}")
        
        if extraction_info.get('vector_rag_extractions'):
            rag_info = extraction_info['vector_rag_extractions']
            print(f"🎯 RAG attempted fields: {rag_info.get('attempted_fields', [])}")
            print(f"✅ RAG successful extractions: {rag_info.get('successful_extractions', [])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Example 1 failed: {e}")
        return False


async def example_advanced_usage():
    """Example 2: Advanced usage with custom configuration."""
    print("\n📋 Example 2: Advanced Vector RAG Pipeline Usage")
    print("=" * 60)
    
    try:
        # Create pipeline with custom settings
        pipeline = VectorRAGPowerPlantPipeline(enable_vector_rag=True)
        
        print("✅ Vector RAG pipeline created")
        
        # Get RAG system information
        rag_stats = pipeline.get_rag_stats()
        print(f"🔧 RAG Configuration:")
        print(f"  - Enabled: {rag_stats['enabled']}")
        print(f"  - Vector DB Path: {rag_stats['vector_db_path']}")
        print(f"  - Embedding Model: {rag_stats['embedding_model']}")
        
        # Extract plant data
        plant_name = "Jhajjar Power Plant"
        org_details, plant_details, extraction_info = await pipeline.extract_plant_data(plant_name)
        
        print(f"✅ Advanced extraction completed")
        
        # Save results with custom filenames
        await pipeline.save_results(
            org_details=org_details,
            plant_details=plant_details,
            extraction_info=extraction_info,
            org_output_path="advanced_org_details.json",
            plant_output_path="advanced_plant_details.json",
            info_output_path="advanced_extraction_info.json"
        )
        
        print("💾 Results saved with custom filenames")
        
        return True
        
    except Exception as e:
        print(f"❌ Example 2 failed: {e}")
        return False


async def example_comparison():
    """Example 3: Compare Vector RAG vs Traditional extraction."""
    print("\n📋 Example 3: Vector RAG vs Traditional Comparison")
    print("=" * 60)
    
    plant_name = "Jhajjar Power Plant"
    
    try:
        # Traditional extraction (RAG disabled)
        print("🔄 Running traditional extraction...")
        _, traditional_plant, traditional_info = await extract_with_vector_rag(
            plant_name=plant_name,
            enable_rag=False
        )
        
        # Vector RAG enhanced extraction
        print("🧠 Running Vector RAG enhanced extraction...")
        _, rag_plant, rag_info = await extract_with_vector_rag(
            plant_name=plant_name,
            enable_rag=True
        )
        
        # Compare results
        print("\n📊 Comparison Results:")
        print(f"Traditional duration: {traditional_info.get('total_duration', 0):.2f}s")
        print(f"Vector RAG duration: {rag_info.get('total_duration', 0):.2f}s")
        
        # Compare field completeness
        traditional_data = traditional_plant.model_dump() if traditional_plant else {}
        rag_data = rag_plant.model_dump() if rag_plant else {}
        
        fields_to_compare = ["lat", "long", "grid_connectivity_maps", "ppa_details", "units_id"]
        
        print("\n🔍 Field Completeness Comparison:")
        for field in fields_to_compare:
            trad_value = traditional_data.get(field)
            rag_value = rag_data.get(field)
            
            trad_complete = bool(trad_value and trad_value != "" and trad_value != [])
            rag_complete = bool(rag_value and rag_value != "" and rag_value != [])
            
            status = "🟢" if rag_complete else "🔴" if trad_complete else "⚪"
            improvement = " (RAG improved)" if rag_complete and not trad_complete else ""
            
            print(f"  {status} {field}: Traditional={trad_complete}, RAG={rag_complete}{improvement}")
        
        return True
        
    except Exception as e:
        print(f"❌ Example 3 failed: {e}")
        return False


async def example_integration_with_existing_clients():
    """Example 4: Integration with existing LLM clients."""
    print("\n📋 Example 4: Integration with Existing LLM Clients")
    print("=" * 60)
    
    try:
        # This example shows how to integrate with your existing LLM clients
        # Note: This requires your actual LLM clients to be properly configured
        
        print("🔧 This example demonstrates integration patterns:")
        print("   1. Import your existing LLM client")
        print("   2. Pass it to the Vector RAG pipeline")
        print("   3. The pipeline will automatically detect and use it")
        
        # Example integration pattern:
        example_code = '''
        # Integration with existing Groq client
        from src.groq_extraction_client import GroqExtractionClient
        from src.vector_rag_pipeline import VectorRAGPowerPlantPipeline
        
        # Create your existing LLM client
        groq_client = GroqExtractionClient()
        
        # Create Vector RAG pipeline with your client
        pipeline = VectorRAGPowerPlantPipeline(enable_vector_rag=True)
        pipeline.llm_client = groq_client  # Use your existing client
        
        # Extract with Vector RAG enhancement
        org_details, plant_details, info = await pipeline.extract_plant_data("Plant Name")
        '''
        
        print("💡 Integration Code Example:")
        print(example_code)
        
        return True
        
    except Exception as e:
        print(f"❌ Example 4 failed: {e}")
        return False


async def main():
    """Run all examples."""
    setup_logging()
    
    print("🚀 Vector RAG Enhanced Pipeline Examples")
    print("=" * 80)
    
    examples = [
        ("Basic Usage", example_basic_usage),
        ("Advanced Usage", example_advanced_usage),
        ("RAG vs Traditional Comparison", example_comparison),
        ("Integration with Existing Clients", example_integration_with_existing_clients)
    ]
    
    results = []
    
    for name, example_func in examples:
        try:
            print(f"\n🎯 Running: {name}")
            success = await example_func()
            results.append((name, success))
        except Exception as e:
            print(f"❌ {name} failed with error: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 Examples Summary:")
    for name, success in results:
        status = "✅" if success else "❌"
        print(f"  {status} {name}")
    
    print("\n🎉 Vector RAG Enhanced Pipeline examples completed!")
    print("📁 Check the generated files and logs for detailed results")
    print("📖 See 'vector_rag_example.log' for detailed execution logs")


if __name__ == "__main__":
    asyncio.run(main())
