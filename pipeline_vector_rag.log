2025-05-29 00:25:44,321 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with Groq
2025-05-29 00:25:44,334 - src.plant_details_extractor - INFO - Plant details extractor initialized with Groq
2025-05-29 00:25:44,334 - src.vector_rag.pipeline_integration - INFO - ✅ Vector RAG integration enabled
2025-05-29 00:25:44,334 - src.vector_rag_pipeline - INFO - Vector RAG Enhanced Pipeline initialized (RAG enabled: True)
2025-05-29 00:25:44,334 - src.vector_rag_pipeline - INFO - 🚀 Starting Vector RAG enhanced extraction for: Jhajjar Power Plant
2025-05-29 00:25:44,334 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-29 00:25:44,334 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-29 00:26:05,151 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-29 00:26:05,152 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-29 00:26:10,419 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-29 00:26:11,420 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:26:12,967 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:26:13,968 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-29 00:26:15,880 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-29 00:26:16,882 - src.simple_pipeline - INFO - Scraping 4/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-29 00:26:18,612 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-29 00:26:19,614 - src.simple_pipeline - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-29 00:26:21,825 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-29 00:26:22,828 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-29 00:26:22,828 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-29 00:26:22,828 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['company_official', 'other', 'wikipedia']}
2025-05-29 00:26:22,829 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-29 00:26:22,829 - src.groq_client - INFO - Starting LLM extraction for 5 content pieces
2025-05-29 00:26:23,360 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:26:23,436 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:26:23,437 - src.groq_client - ERROR - Groq extraction failed for field cfpp_type: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99941, Requested 1246. Please try again in 17m5.49s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:26:23,437 - src.groq_client - INFO - Extracted cfpp_type: None (confidence: 0.00)
2025-05-29 00:26:24,043 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:26:24,045 - src.groq_client - ERROR - Groq extraction failed for field country_name: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99941, Requested 1134. Please try again in 15m28.113999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:26:24,046 - src.groq_client - INFO - Extracted country_name: None (confidence: 0.00)
2025-05-29 00:26:24,624 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:26:24,626 - src.groq_client - ERROR - Groq extraction failed for field province: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99940, Requested 1152. Please try again in 15m43.096s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:26:24,626 - src.groq_client - INFO - Extracted province: None (confidence: 0.00)
2025-05-29 00:26:25,197 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:26:25,199 - src.groq_client - ERROR - Groq extraction failed for field plants_count: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99939, Requested 1134. Please try again in 15m26.964s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:26:25,199 - src.groq_client - INFO - Extracted plants_count: None (confidence: 0.00)
2025-05-29 00:26:25,766 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:26:25,767 - src.groq_client - ERROR - Groq extraction failed for field plant_types: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99939, Requested 1153. Please try again in 15m42.81s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:26:25,768 - src.groq_client - INFO - Extracted plant_types: None (confidence: 0.00)
2025-05-29 00:26:26,345 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:26:26,346 - src.groq_client - ERROR - Groq extraction failed for field ppa_flag: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99938, Requested 1206. Please try again in 16m28.024s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:26:26,346 - src.groq_client - INFO - Extracted ppa_flag: None (confidence: 0.00)
2025-05-29 00:26:26,923 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:26:26,925 - src.groq_client - ERROR - Groq extraction failed for field currency_in: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99937, Requested 1160. Please try again in 15m47.702s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:26:26,925 - src.groq_client - INFO - Extracted currency_in: None (confidence: 0.00)
2025-05-29 00:26:27,511 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:26:27,513 - src.groq_client - ERROR - Groq extraction failed for field financial_year: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99937, Requested 1280. Please try again in 17m30.813999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:26:27,513 - src.groq_client - INFO - Extracted financial_year: None (confidence: 0.00)
2025-05-29 00:26:28,015 - src.groq_client - INFO - LLM extraction completed
2025-05-29 00:26:28,015 - src.simple_pipeline - INFO - ✅ Organizational extraction completed
2025-05-29 00:26:28,015 - src.simple_pipeline - INFO - 🔧 Step 4: Extracting plant details with smart field-by-field approach
2025-05-29 00:26:28,016 - src.simple_pipeline - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-29 00:26:28,016 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-29 00:26:28,129 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:26:28,130 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field name: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99936, Requested 1716. Please try again in 23m46.878s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:26:28,131 - src.plant_details_extractor - INFO - Extracted name: None (confidence: 0.00)
2025-05-29 00:26:28,708 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:26:28,709 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_type: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99935, Requested 1737. Please try again in 24m4.442s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:26:28,710 - src.plant_details_extractor - INFO - Extracted plant_type: None (confidence: 0.00)
2025-05-29 00:26:29,278 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:26:29,280 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_address: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99935, Requested 1730. Please try again in 23m57.826s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:26:29,280 - src.plant_details_extractor - INFO - Extracted plant_address: None (confidence: 0.00)
2025-05-29 00:26:29,863 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:26:29,864 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field lat: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99934, Requested 1736. Please try again in 24m2.427s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:26:29,864 - src.plant_details_extractor - INFO - Extracted lat: None (confidence: 0.00)
2025-05-29 00:26:30,456 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:26:30,457 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field long: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99933, Requested 1737. Please try again in 24m2.713s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:26:30,457 - src.plant_details_extractor - INFO - Extracted long: None (confidence: 0.00)
2025-05-29 00:26:31,035 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:26:31,037 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99933, Requested 1726. Please try again in 23m52.619s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:26:31,037 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-29 00:26:31,538 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-29 00:26:31,538 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "capacity"'
2025-05-29 00:26:31,538 - src.plant_details_extractor - INFO - Using organization name for plant name: CLP India Private Limited
2025-05-29 00:26:31,539 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-29 00:26:31,539 - src.simple_pipeline - INFO - ✅ Field 'name' extracted from cache
2025-05-29 00:26:31,539 - src.simple_pipeline - INFO - ✅ Field 'plant_id' extracted from cache
2025-05-29 00:26:31,539 - src.simple_pipeline - INFO - 🔍 Field 'lat' is missing - will search specifically
2025-05-29 00:26:31,539 - src.simple_pipeline - INFO - 🔍 Field 'long' is missing - will search specifically
2025-05-29 00:26:31,539 - src.simple_pipeline - INFO - 🔍 Field 'plant_address' is missing - will search specifically
2025-05-29 00:26:31,539 - src.simple_pipeline - INFO - 🔍 Field 'grid_connectivity_maps' is missing - will search specifically
2025-05-29 00:26:31,539 - src.simple_pipeline - INFO - 🔍 Field 'ppa_details' is missing - will search specifically
2025-05-29 00:26:31,540 - src.simple_pipeline - INFO - 🔍 Field 'plant_type' is missing - will search specifically
2025-05-29 00:26:31,540 - src.simple_pipeline - INFO - 🔍 Field 'units_id' is missing - will search specifically
2025-05-29 00:26:31,540 - src.simple_pipeline - INFO - 🎯 Starting targeted searches for 7 missing fields
2025-05-29 00:26:31,540 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'lat' data
2025-05-29 00:26:31,540 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant latitude coordinates GPS location'
2025-05-29 00:27:34,833 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with Groq
2025-05-29 00:27:34,846 - src.plant_details_extractor - INFO - Plant details extractor initialized with Groq
2025-05-29 00:27:34,846 - src.vector_rag.pipeline_integration - INFO - ✅ Vector RAG integration enabled
2025-05-29 00:27:34,846 - src.vector_rag_pipeline - INFO - Vector RAG Enhanced Pipeline initialized (RAG enabled: True)
2025-05-29 00:27:34,846 - src.vector_rag_pipeline - INFO - 🚀 Starting Vector RAG enhanced extraction for: Jhajjar Power Plant
2025-05-29 00:27:34,846 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-29 00:27:34,846 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-29 00:27:44,302 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-29 00:27:44,302 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-29 00:27:46,089 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-29 00:27:47,090 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:27:49,425 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:27:50,427 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-29 00:27:52,540 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-29 00:27:53,541 - src.simple_pipeline - INFO - Scraping 4/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-29 00:27:55,197 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-29 00:27:56,198 - src.simple_pipeline - INFO - Scraping 5/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-29 00:27:58,121 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-29 00:27:59,123 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-29 00:27:59,124 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-29 00:27:59,124 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['company_official', 'other', 'wikipedia']}
2025-05-29 00:27:59,124 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-29 00:27:59,124 - src.groq_client - INFO - Starting LLM extraction for 5 content pieces
2025-05-29 00:27:59,381 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:27:59,384 - src.groq_client - ERROR - Groq extraction failed for field organization_name: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99830, Requested 1172. Please try again in 14m25.610999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:27:59,494 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:27:59,496 - src.groq_client - ERROR - Groq extraction failed for field cfpp_type: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99830, Requested 1250. Please try again in 15m32.888s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:27:59,496 - src.groq_client - INFO - Extracted cfpp_type: None (confidence: 0.00)
2025-05-29 00:28:00,151 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:28:00,152 - src.groq_client - ERROR - Groq extraction failed for field country_name: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99829, Requested 1138. Please try again in 13m55.467s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:28:00,152 - src.groq_client - INFO - Extracted country_name: None (confidence: 0.00)
2025-05-29 00:28:00,731 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:28:00,732 - src.groq_client - ERROR - Groq extraction failed for field province: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99829, Requested 1156. Please try again in 14m10.435s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:28:00,732 - src.groq_client - INFO - Extracted province: None (confidence: 0.00)
2025-05-29 00:28:01,298 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:28:01,300 - src.groq_client - ERROR - Groq extraction failed for field plants_count: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99828, Requested 1137. Please try again in 13m53.454s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:28:01,300 - src.groq_client - INFO - Extracted plants_count: None (confidence: 0.00)
2025-05-29 00:28:01,882 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:28:01,883 - src.groq_client - ERROR - Groq extraction failed for field plant_types: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99827, Requested 1157. Please try again in 14m10.15s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:28:01,884 - src.groq_client - INFO - Extracted plant_types: None (confidence: 0.00)
2025-05-29 00:28:02,460 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:28:02,461 - src.groq_client - ERROR - Groq extraction failed for field ppa_flag: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99827, Requested 1210. Please try again in 14m55.363s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:28:02,461 - src.groq_client - INFO - Extracted ppa_flag: None (confidence: 0.00)
2025-05-29 00:28:03,029 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:28:03,030 - src.groq_client - ERROR - Groq extraction failed for field currency_in: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99826, Requested 1162. Please try again in 14m13.322s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:28:03,031 - src.groq_client - INFO - Extracted currency_in: None (confidence: 0.00)
2025-05-29 00:28:03,602 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:28:03,604 - src.groq_client - ERROR - Groq extraction failed for field financial_year: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99825, Requested 1282. Please try again in 15m56.431s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:28:03,604 - src.groq_client - INFO - Extracted financial_year: None (confidence: 0.00)
2025-05-29 00:28:04,106 - src.groq_client - INFO - LLM extraction completed
2025-05-29 00:28:04,106 - src.simple_pipeline - INFO - ✅ Organizational extraction completed
2025-05-29 00:28:04,107 - src.simple_pipeline - INFO - 🔧 Step 4: Extracting plant details with smart field-by-field approach
2025-05-29 00:28:04,107 - src.simple_pipeline - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-29 00:28:04,107 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-29 00:28:04,226 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:28:04,227 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field name: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99825, Requested 1716. Please try again in 22m10.789s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:28:04,227 - src.plant_details_extractor - INFO - Extracted name: None (confidence: 0.00)
2025-05-29 00:28:04,800 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:28:04,801 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_type: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99824, Requested 1737. Please try again in 22m28.352s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:28:04,802 - src.plant_details_extractor - INFO - Extracted plant_type: None (confidence: 0.00)
2025-05-29 00:28:05,633 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:28:05,634 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_address: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99823, Requested 1730. Please try again in 22m21.471s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:28:05,635 - src.plant_details_extractor - INFO - Extracted plant_address: None (confidence: 0.00)
2025-05-29 00:28:06,254 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:28:06,255 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field lat: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99822, Requested 1736. Please try again in 22m26.032s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:28:06,256 - src.plant_details_extractor - INFO - Extracted lat: None (confidence: 0.00)
2025-05-29 00:28:06,835 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:28:06,836 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field long: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99822, Requested 1737. Please try again in 22m26.32s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:28:06,837 - src.plant_details_extractor - INFO - Extracted long: None (confidence: 0.00)
2025-05-29 00:28:07,403 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:28:07,403 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99821, Requested 1726. Please try again in 22m16.242s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:28:07,404 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-29 00:28:07,905 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-29 00:28:07,906 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "capacity"'
2025-05-29 00:28:07,906 - src.plant_details_extractor - INFO - Using input plant name as fallback: Jhajjar Power Plant
2025-05-29 00:28:07,906 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-29 00:28:07,906 - src.simple_pipeline - INFO - ✅ Field 'name' extracted from cache
2025-05-29 00:28:07,906 - src.simple_pipeline - INFO - ✅ Field 'plant_id' extracted from cache
2025-05-29 00:28:07,906 - src.simple_pipeline - INFO - 🔍 Field 'lat' is missing - will search specifically
2025-05-29 00:28:07,907 - src.simple_pipeline - INFO - 🔍 Field 'long' is missing - will search specifically
2025-05-29 00:28:07,907 - src.simple_pipeline - INFO - 🔍 Field 'plant_address' is missing - will search specifically
2025-05-29 00:28:07,907 - src.simple_pipeline - INFO - 🔍 Field 'grid_connectivity_maps' is missing - will search specifically
2025-05-29 00:28:07,907 - src.simple_pipeline - INFO - 🔍 Field 'ppa_details' is missing - will search specifically
2025-05-29 00:28:07,907 - src.simple_pipeline - INFO - 🔍 Field 'plant_type' is missing - will search specifically
2025-05-29 00:28:07,907 - src.simple_pipeline - INFO - 🔍 Field 'units_id' is missing - will search specifically
2025-05-29 00:28:07,907 - src.simple_pipeline - INFO - 🎯 Starting targeted searches for 7 missing fields
2025-05-29 00:28:07,908 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'lat' data
2025-05-29 00:28:07,908 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant latitude coordinates GPS location'
2025-05-29 00:28:16,186 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:28:17,438 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:28:18,439 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-29 00:28:19,072 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-29 00:28:20,073 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-29 00:28:20,926 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-29 00:28:21,928 - src.simple_pipeline - INFO - ✅ Found targeted content for 'lat' - 3 sources
2025-05-29 00:28:21,931 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'lat' using RAG: 28.607111
2025-05-29 00:28:21,931 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'long' data
2025-05-29 00:28:21,931 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant longitude coordinates GPS location'
2025-05-29 00:28:52,266 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:28:54,953 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:28:55,954 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-29 00:29:01,035 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-29 00:29:02,037 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-29 00:29:02,762 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-29 00:29:03,764 - src.simple_pipeline - INFO - ✅ Found targeted content for 'long' - 3 sources
2025-05-29 00:29:03,767 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'long' using RAG: 76.6565
2025-05-29 00:29:03,767 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'plant_address' data
2025-05-29 00:29:03,767 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant complete address location street city'
2025-05-29 00:29:22,978 - src.simple_pipeline - INFO - Scraping 1/2: https://www.bloomberg.com/profile/company/0808736D:IN
2025-05-29 00:29:26,250 - src.simple_pipeline - INFO - Successfully scraped https://www.bloomberg.com/profile/company/0808736D:IN (193 chars)
2025-05-29 00:29:27,252 - src.simple_pipeline - INFO - Scraping 2/2: https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html
2025-05-29 00:29:27,489 - src.scraper_client - WARNING - Failed to scrape https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html: HTTP 403
2025-05-29 00:29:27,489 - src.simple_pipeline - WARNING - Content too short or failed for https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html
2025-05-29 00:29:28,492 - src.simple_pipeline - INFO - ✅ Found targeted content for 'plant_address' - 1 sources
2025-05-29 00:29:28,493 - src.simple_pipeline - INFO - 📝 Content found for 'plant_address' but RAG extraction failed
2025-05-29 00:29:28,493 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-29 00:29:28,494 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant grid connection substation transmission line'
2025-05-29 00:29:34,874 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-29 00:29:42,144 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-29 00:29:42,146 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-29 00:29:42,146 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-29 00:29:43,147 - src.simple_pipeline - INFO - Scraping 2/3: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-29 00:30:01,963 - src.pdf_processor - INFO - Successfully extracted 112384 chars from PDF using pdfplumber: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-29 00:30:01,965 - src.scraper_client - INFO - Successfully processed PDF: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars, score: 1.00)
2025-05-29 00:30:01,965 - src.simple_pipeline - INFO - Successfully scraped http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars)
2025-05-29 00:30:02,967 - src.simple_pipeline - INFO - Scraping 3/3: https://www.indigrid.co.in/portfolio-assets/jhajjar-kt-transco-pvt-ltd-jktpl/
2025-05-29 00:30:05,630 - src.simple_pipeline - INFO - Successfully scraped https://www.indigrid.co.in/portfolio-assets/jhajjar-kt-transco-pvt-ltd-jktpl/ (846 chars)
2025-05-29 00:30:06,632 - src.simple_pipeline - INFO - ✅ Found targeted content for 'grid_connectivity_maps' - 3 sources
2025-05-29 00:30:06,635 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'grid_connectivity_maps' using RAG: [{'description': 'Grid connectivity details for Jhajjar Power Plant', 'details': [{'substation_name': 'Sonipat Substation', 'substation_type': 'transmission', 'capacity': '400 kV', 'latitude': '', 'longitude': '', 'description': 'Primary transmission connection point - approximately 70 km northeast', 'projects': [{'description': '400 kV transmission line from Jhajjar to Sonipat', 'distance': '70 km'}]}, {'substation_name': 'Mahendragarh Substation', 'substation_type': 'transmission', 'capacity': '400 kV', 'latitude': '', 'longitude': '', 'description': 'Secondary transmission connection point - approximately 50 km southwest', 'projects': [{'description': '400 kV transmission line from Jhajjar to Mahendragarh', 'distance': '50 km'}]}]}]
2025-05-29 00:30:06,635 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'ppa_details' data
2025-05-29 00:30:06,635 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant power purchase agreement PPA contract details'
2025-05-29 00:30:13,098 - src.simple_pipeline - INFO - Scraping 1/3: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-29 00:30:19,930 - src.pdf_processor - INFO - Successfully extracted 36539 chars from PDF using pdfplumber: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-29 00:30:19,931 - src.scraper_client - INFO - Successfully processed PDF: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars, score: 1.00)
2025-05-29 00:30:19,931 - src.simple_pipeline - INFO - Successfully scraped https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars)
2025-05-29 00:30:20,933 - src.simple_pipeline - INFO - Scraping 2/3: https://cercind.gov.in/2022/orders/363-MP-2019.pdf
2025-05-29 00:30:30,035 - src.pdf_processor - INFO - Successfully extracted 17990 chars from PDF using pdfplumber: https://cercind.gov.in/2022/orders/363-MP-2019.pdf
2025-05-29 00:30:30,036 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2022/orders/363-MP-2019.pdf (17981 chars, score: 1.00)
2025-05-29 00:30:30,036 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2022/orders/363-MP-2019.pdf (17981 chars)
2025-05-29 00:30:31,037 - src.simple_pipeline - INFO - Scraping 3/3: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-29 00:30:40,717 - src.pdf_processor - INFO - Successfully extracted 111606 chars from PDF using pdfplumber: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-29 00:30:40,718 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars, score: 1.00)
2025-05-29 00:30:40,718 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars)
2025-05-29 00:30:41,720 - src.simple_pipeline - INFO - ✅ Found targeted content for 'ppa_details' - 3 sources
2025-05-29 00:30:41,723 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'ppa_details' using RAG: [{'description': 'Power Purchase Agreement for Jhajjar Power Plant', 'capacity': '1320 MW', 'capacity_unit': 'MW', 'start_date': '2012', 'end_date': '', 'tenure': 30, 'tenure_type': 'Years', 'respondents': [{'name': 'Haryana State Distribution Companies', 'capacity': '1188 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}, {'name': 'External Power Buyers', 'capacity': '132 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}]}]
2025-05-29 00:30:41,723 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'plant_type' data
2025-05-29 00:30:41,723 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant power plant type coal gas nuclear solar wind'
2025-05-29 00:31:14,129 - src.simple_pipeline - INFO - Scraping 1/3: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-29 00:31:15,954 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-29 00:31:16,956 - src.simple_pipeline - INFO - Scraping 2/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:31:17,889 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:31:18,890 - src.simple_pipeline - INFO - Scraping 3/3: https://apraava.com/projects/jhajjar-power-plant
2025-05-29 00:31:21,276 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-29 00:31:22,278 - src.simple_pipeline - INFO - ✅ Found targeted content for 'plant_type' - 3 sources
2025-05-29 00:31:22,278 - src.simple_pipeline - INFO - 📝 Content found for 'plant_type' but RAG extraction failed
2025-05-29 00:31:22,278 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'units_id' data
2025-05-29 00:31:22,278 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant units generators unit numbers'
2025-05-29 00:31:37,579 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-29 00:31:43,735 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-29 00:31:43,738 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-29 00:31:43,738 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-29 00:31:44,739 - src.simple_pipeline - INFO - Scraping 2/3: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf
2025-05-29 00:31:53,466 - src.pdf_processor - INFO - Successfully extracted 89455 chars from PDF using pdfplumber: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf
2025-05-29 00:31:53,467 - src.scraper_client - INFO - Successfully processed PDF: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf (50003 chars, score: 1.00)
2025-05-29 00:31:53,467 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf (50003 chars)
2025-05-29 00:31:54,469 - src.simple_pipeline - INFO - Scraping 3/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:31:58,927 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:31:59,930 - src.simple_pipeline - INFO - ✅ Found targeted content for 'units_id' - 3 sources
2025-05-29 00:31:59,938 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'units_id' using RAG: ['Unit 1', 'Unit 3', 'Unit 250']
2025-05-29 00:31:59,944 - src.simple_pipeline - INFO - 💾 Targeted content saved to jhajjar_power_plant_targeted_content_20250529_003159.json
2025-05-29 00:31:59,945 - src.simple_pipeline - ERROR - Error creating PlantDetails object: 3 validation errors for PlantDetails
units_id.0
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
units_id.1
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 3', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
units_id.2
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 250', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
2025-05-29 00:31:59,945 - src.simple_pipeline - INFO - 🎉 Simplified extraction completed for: Jhajjar Power Plant
2025-05-29 00:31:59,945 - src.simple_pipeline - INFO - ⏱️  Total time: 265.1s
2025-05-29 00:31:59,945 - src.simple_pipeline - INFO - 💾 Cache efficiency: 2 fields from cache, 7 targeted searches
2025-05-29 00:31:59,946 - src.vector_rag_pipeline - INFO - 🧠 Starting Vector RAG enhancement phase
2025-05-29 00:31:59,946 - src.vector_rag_pipeline - INFO - 📁 Using targeted content file: jhajjar_power_plant_targeted_content_20250529_003159.json
2025-05-29 00:31:59,946 - src.vector_rag_pipeline - INFO - 🎯 Fields to enhance with Vector RAG: ['plant_address']
2025-05-29 00:31:59,946 - src.vector_rag.pipeline_integration - INFO - 🚀 Starting Vector RAG processing for Jhajjar Power Plant
2025-05-29 00:31:59,946 - src.vector_rag.pipeline_integration - INFO - 📁 Targeted content file: jhajjar_power_plant_targeted_content_20250529_003159.json
2025-05-29 00:31:59,946 - src.vector_rag.pipeline_integration - INFO - 🎯 Missing fields to extract: ['plant_address']
2025-05-29 00:31:59,946 - src.vector_rag.pipeline_integration - ERROR - Error creating vector database: Vector dependencies not available. Please install: pip install faiss-cpu sentence-transformers numpy
2025-05-29 00:31:59,946 - src.vector_rag.pipeline_integration - ERROR - Failed to create vector database
2025-05-29 00:31:59,946 - src.vector_rag_pipeline - ERROR - Error in Vector RAG enhancement: 3 validation errors for PlantDetails
units_id.0
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
units_id.1
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 3', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
units_id.2
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 250', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
2025-05-29 00:31:59,946 - src.vector_rag_pipeline - INFO - ✅ Vector RAG enhanced extraction completed in 265.10s
2025-05-29 00:31:59,947 - src.simple_pipeline - INFO - 📊 Organizational results saved to vector_rag_org_20250529_003159.json
2025-05-29 00:31:59,947 - src.simple_pipeline - ERROR - Error saving results: 'dict' object has no attribute 'model_dump'
2025-05-29 00:31:59,948 - src.vector_rag_pipeline - INFO - Vector RAG statistics saved to vector_rag_stats.json
2025-05-29 00:43:22,275 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with Groq
2025-05-29 00:43:22,289 - src.plant_details_extractor - INFO - Plant details extractor initialized with Groq
2025-05-29 00:43:22,289 - src.vector_rag_pipeline - WARNING - Failed to setup OpenAI clients: name 'config' is not defined, using Groq
2025-05-29 00:43:22,289 - src.vector_rag.pipeline_integration - INFO - ✅ Vector RAG integration enabled
2025-05-29 00:43:22,289 - src.vector_rag_pipeline - INFO - Vector RAG Enhanced Pipeline initialized (RAG enabled: True, OpenAI: True)
2025-05-29 00:43:22,289 - src.vector_rag_pipeline - INFO - 🚀 Starting Vector RAG enhanced extraction for: Adani Mundra Power Plant
2025-05-29 00:43:22,289 - src.vector_rag_pipeline - INFO - 📊 Running full pipeline (no existing content or Vector RAG disabled)
2025-05-29 00:43:22,289 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Adani Mundra Power Plant
2025-05-29 00:43:22,289 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Adani Mundra Power Plant' - getting top 5 links
2025-05-29 00:43:26,644 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-29 00:43:26,644 - src.simple_pipeline - INFO - Scraping 1/5: https://www.power-technology.com/projects/mundra-plant/
2025-05-29 00:43:29,211 - src.simple_pipeline - INFO - Successfully scraped https://www.power-technology.com/projects/mundra-plant/ (4746 chars)
2025-05-29 00:43:30,212 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Mundra_Thermal_Power_Station
2025-05-29 00:43:33,918 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Mundra_Thermal_Power_Station (1168 chars)
2025-05-29 00:43:34,919 - src.simple_pipeline - INFO - Scraping 3/5: https://www.gem.wiki/Mundra_Thermal_Power_Project_(Adani)
2025-05-29 00:45:21,134 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with Groq
2025-05-29 00:45:21,149 - src.plant_details_extractor - INFO - Plant details extractor initialized with Groq
2025-05-29 00:45:21,149 - src.vector_rag_pipeline - WARNING - Failed to setup OpenAI clients: name 'config' is not defined, using Groq
2025-05-29 00:45:21,149 - src.vector_rag.pipeline_integration - INFO - ✅ Vector RAG integration enabled
2025-05-29 00:45:21,150 - src.vector_rag_pipeline - INFO - Vector RAG Enhanced Pipeline initialized (RAG enabled: True, OpenAI: True)
2025-05-29 00:45:21,150 - src.vector_rag_pipeline - INFO - 🚀 Starting Vector RAG enhanced extraction for: Test Plant
2025-05-29 00:45:21,150 - src.vector_rag_pipeline - INFO - 📊 Running full pipeline (no existing content or Vector RAG disabled)
2025-05-29 00:45:21,150 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Test Plant
2025-05-29 00:45:21,150 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Test Plant' - getting top 5 links
2025-05-29 00:45:24,396 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 3 top results
2025-05-29 00:45:24,396 - src.simple_pipeline - INFO - Scraping 1/3: https://www.matconibc.com/blog/what-is-a-matcon-test-plant-and-what-is-it-used-for
2025-05-29 00:45:25,592 - src.simple_pipeline - INFO - Successfully scraped https://www.matconibc.com/blog/what-is-a-matcon-test-plant-and-what-is-it-used-for (5113 chars)
2025-05-29 00:45:26,594 - src.simple_pipeline - INFO - Scraping 2/3: https://heritagegrowers.com/product/test-plant/
2025-05-29 00:45:30,230 - src.simple_pipeline - INFO - Successfully scraped https://heritagegrowers.com/product/test-plant/ (183 chars)
2025-05-29 00:45:31,232 - src.simple_pipeline - INFO - Scraping 3/3: https://soiltestlab.com/agriculture/plant-tests/
2025-05-29 00:45:36,004 - src.simple_pipeline - INFO - Successfully scraped https://soiltestlab.com/agriculture/plant-tests/ (1880 chars)
2025-05-29 00:45:37,006 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 3 pages scraped
2025-05-29 00:45:37,006 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-29 00:45:37,007 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 3, 'total_content': 7176, 'avg_relevance': 0.09333333333333334, 'high_quality_sources': 0, 'source_type_diversity': 1, 'source_types': ['other']}
2025-05-29 00:45:37,007 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-29 00:45:37,007 - src.groq_client - INFO - Starting LLM extraction for 3 content pieces
2025-05-29 00:45:37,389 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:45:37,443 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:45:37,444 - src.groq_client - ERROR - Groq extraction failed for field cfpp_type: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99585, Requested 1244. Please try again in 11m56.198s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:45:37,444 - src.groq_client - INFO - Extracted cfpp_type: None (confidence: 0.00)
2025-05-29 00:45:37,991 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:45:37,993 - src.groq_client - ERROR - Groq extraction failed for field country_name: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99585, Requested 1132. Please try again in 10m18.882s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:45:37,994 - src.groq_client - INFO - Extracted country_name: None (confidence: 0.00)
2025-05-29 00:45:38,560 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:45:38,562 - src.groq_client - ERROR - Groq extraction failed for field province: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99584, Requested 1150. Please try again in 10m33.874s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:45:38,562 - src.groq_client - INFO - Extracted province: None (confidence: 0.00)
2025-05-29 00:45:39,107 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:45:39,110 - src.groq_client - ERROR - Groq extraction failed for field plants_count: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99584, Requested 1131. Please try again in 10m16.902s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:45:39,111 - src.groq_client - INFO - Extracted plants_count: None (confidence: 0.00)
2025-05-29 00:45:39,792 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:45:39,795 - src.groq_client - ERROR - Groq extraction failed for field plant_types: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99583, Requested 1151. Please try again in 10m33.498s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:45:39,795 - src.groq_client - INFO - Extracted plant_types: None (confidence: 0.00)
2025-05-29 00:45:40,344 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:45:40,345 - src.groq_client - ERROR - Groq extraction failed for field ppa_flag: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99582, Requested 1204. Please try again in 11m18.738s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:45:40,346 - src.groq_client - INFO - Extracted ppa_flag: None (confidence: 0.00)
2025-05-29 00:45:40,897 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:45:40,899 - src.groq_client - ERROR - Groq extraction failed for field currency_in: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99581, Requested 1155. Please try again in 10m35.848s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:45:40,899 - src.groq_client - INFO - Extracted currency_in: None (confidence: 0.00)
2025-05-29 00:45:41,447 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:45:41,449 - src.groq_client - ERROR - Groq extraction failed for field financial_year: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99581, Requested 1275. Please try again in 12m18.978s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:45:41,449 - src.groq_client - INFO - Extracted financial_year: None (confidence: 0.00)
2025-05-29 00:45:41,951 - src.groq_client - INFO - LLM extraction completed
2025-05-29 00:45:41,951 - src.simple_pipeline - INFO - ✅ Organizational extraction completed
2025-05-29 00:45:41,952 - src.simple_pipeline - INFO - 🔧 Step 4: Extracting plant details with smart field-by-field approach
2025-05-29 00:45:41,952 - src.simple_pipeline - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-29 00:45:41,952 - src.plant_details_extractor - INFO - Starting plant details extraction for: Test Plant
2025-05-29 00:45:42,061 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:45:42,062 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field name: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99580, Requested 1195. Please try again in 11m9.248s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:45:42,062 - src.plant_details_extractor - INFO - Extracted name: None (confidence: 0.00)
2025-05-29 00:45:42,608 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:45:42,610 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_type: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99579, Requested 1219. Please try again in 11m29.434s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:45:42,611 - src.plant_details_extractor - INFO - Extracted plant_type: None (confidence: 0.00)
2025-05-29 00:45:43,169 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:45:43,170 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_address: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99579, Requested 1211. Please try again in 11m21.968999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:45:43,170 - src.plant_details_extractor - INFO - Extracted plant_address: None (confidence: 0.00)
2025-05-29 00:45:43,727 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:45:43,729 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field lat: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99578, Requested 1217. Please try again in 11m26.594s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:45:43,729 - src.plant_details_extractor - INFO - Extracted lat: None (confidence: 0.00)
2025-05-29 00:45:44,276 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:45:44,277 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field long: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99578, Requested 1218. Please try again in 11m26.905s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:45:44,278 - src.plant_details_extractor - INFO - Extracted long: None (confidence: 0.00)
2025-05-29 00:45:44,854 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:45:44,855 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99577, Requested 1207. Please try again in 11m16.826999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:45:44,856 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-29 00:45:45,357 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-29 00:45:45,358 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "capacity"'
2025-05-29 00:45:45,358 - src.plant_details_extractor - INFO - Using organization name for plant name: Matcon
2025-05-29 00:45:45,358 - src.plant_details_extractor - INFO - Plant details extraction completed for: Test Plant
2025-05-29 00:45:45,359 - src.simple_pipeline - INFO - ✅ Field 'name' extracted from cache
2025-05-29 00:45:45,359 - src.simple_pipeline - INFO - ✅ Field 'plant_id' extracted from cache
2025-05-29 00:45:45,359 - src.simple_pipeline - INFO - 🔍 Field 'lat' is missing - will search specifically
2025-05-29 00:45:45,359 - src.simple_pipeline - INFO - 🔍 Field 'long' is missing - will search specifically
2025-05-29 00:45:45,359 - src.simple_pipeline - INFO - 🔍 Field 'plant_address' is missing - will search specifically
2025-05-29 00:45:45,360 - src.simple_pipeline - INFO - 🔍 Field 'grid_connectivity_maps' is missing - will search specifically
2025-05-29 00:45:45,360 - src.simple_pipeline - INFO - 🔍 Field 'ppa_details' is missing - will search specifically
2025-05-29 00:45:45,360 - src.simple_pipeline - INFO - 🔍 Field 'plant_type' is missing - will search specifically
2025-05-29 00:45:45,360 - src.simple_pipeline - INFO - 🔍 Field 'units_id' is missing - will search specifically
2025-05-29 00:45:45,360 - src.simple_pipeline - INFO - 🎯 Starting targeted searches for 7 missing fields
2025-05-29 00:45:45,361 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'lat' data
2025-05-29 00:45:45,361 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Test Plant latitude coordinates GPS location'
2025-05-29 00:45:48,037 - src.simple_pipeline - INFO - Scraping 1/3: https://gps-coordinates.org/
2025-05-29 00:45:49,332 - src.simple_pipeline - INFO - Successfully scraped https://gps-coordinates.org/ (9237 chars)
2025-05-29 00:45:50,333 - src.simple_pipeline - INFO - Scraping 2/3: https://www.gps-coordinates.net/
2025-05-29 00:45:51,374 - src.simple_pipeline - INFO - Successfully scraped https://www.gps-coordinates.net/ (1836 chars)
2025-05-29 00:45:52,376 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/
2025-05-29 00:45:53,151 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/ (2034 chars)
2025-05-29 00:45:54,154 - src.simple_pipeline - INFO - ✅ Found targeted content for 'lat' - 3 sources
2025-05-29 00:45:54,157 - src.simple_pipeline - INFO - 📝 Content found for 'lat' but RAG extraction failed
2025-05-29 00:45:54,157 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'long' data
2025-05-29 00:45:54,158 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Test Plant longitude coordinates GPS location'
2025-05-29 00:45:57,361 - src.simple_pipeline - INFO - Scraping 1/3: https://gps-coordinates.org/
2025-05-29 00:45:58,969 - src.simple_pipeline - INFO - Successfully scraped https://gps-coordinates.org/ (9237 chars)
2025-05-29 00:45:59,970 - src.simple_pipeline - INFO - Scraping 2/3: https://www.gps-coordinates.net/
2025-05-29 00:46:00,686 - src.simple_pipeline - INFO - Successfully scraped https://www.gps-coordinates.net/ (1836 chars)
2025-05-29 00:46:01,687 - src.simple_pipeline - INFO - Scraping 3/3: https://support.google.com/maps/answer/18539?hl=en&co=GENIE.Platform%3DDesktop
2025-05-29 00:46:08,517 - src.simple_pipeline - INFO - Successfully scraped https://support.google.com/maps/answer/18539?hl=en&co=GENIE.Platform%3DDesktop (223 chars)
2025-05-29 00:46:09,519 - src.simple_pipeline - INFO - ✅ Found targeted content for 'long' - 3 sources
2025-05-29 00:46:09,522 - src.simple_pipeline - INFO - 📝 Content found for 'long' but RAG extraction failed
2025-05-29 00:46:09,522 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'plant_address' data
2025-05-29 00:46:09,522 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Test Plant complete address location street city'
2025-05-29 00:46:14,059 - src.simple_pipeline - INFO - Scraping 1/3: https://www.borgwarner.com/company/locations
2025-05-29 00:46:19,815 - readability.readability - INFO - ruthless removal did not work. 
2025-05-29 00:46:19,845 - src.simple_pipeline - WARNING - Content too short or failed for https://www.borgwarner.com/company/locations
2025-05-29 00:46:20,847 - src.simple_pipeline - INFO - Scraping 2/3: https://westinghousenuclear.com/about/locations/
2025-05-29 00:46:22,210 - src.simple_pipeline - INFO - Successfully scraped https://westinghousenuclear.com/about/locations/ (4367 chars)
2025-05-29 00:46:23,212 - src.simple_pipeline - INFO - Scraping 3/3: https://news.ycombinator.com/item?id=30733339
2025-05-29 00:46:23,936 - readability.readability - INFO - ruthless removal did not work. 
2025-05-29 00:46:23,937 - src.simple_pipeline - WARNING - Content too short or failed for https://news.ycombinator.com/item?id=30733339
2025-05-29 00:46:24,938 - src.simple_pipeline - INFO - ✅ Found targeted content for 'plant_address' - 1 sources
2025-05-29 00:46:24,939 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'plant_address' using RAG: **South Africa**
2025-05-29 00:46:24,939 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-29 00:46:24,939 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Test Plant grid connection substation transmission line'
2025-05-29 00:47:22,728 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with Groq
2025-05-29 00:47:22,741 - src.plant_details_extractor - INFO - Plant details extractor initialized with Groq
2025-05-29 00:47:22,741 - src.vector_rag_pipeline - WARNING - Failed to setup OpenAI clients: name 'config' is not defined, using Groq
2025-05-29 00:47:22,741 - src.vector_rag.pipeline_integration - INFO - ✅ Vector RAG integration enabled
2025-05-29 00:47:22,741 - src.vector_rag_pipeline - INFO - Vector RAG Enhanced Pipeline initialized (RAG enabled: True, OpenAI: True)
2025-05-29 00:47:22,742 - src.vector_rag_pipeline - INFO - 🚀 Starting Vector RAG enhanced extraction for: Plant Name
2025-05-29 00:47:22,742 - src.vector_rag_pipeline - INFO - 📊 Running full pipeline (no existing content or Vector RAG disabled)
2025-05-29 00:47:22,742 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Plant Name
2025-05-29 00:47:22,742 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Plant Name' - getting top 5 links
2025-05-29 00:47:36,602 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 2 top results
2025-05-29 00:47:36,603 - src.simple_pipeline - INFO - Scraping 1/2: https://www.thespruce.com/plants-a-to-z-5116344
2025-05-29 00:47:46,855 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with Groq
2025-05-29 00:47:46,868 - src.plant_details_extractor - INFO - Plant details extractor initialized with Groq
2025-05-29 00:47:46,869 - src.vector_rag_pipeline - WARNING - Failed to setup OpenAI clients: name 'config' is not defined, using Groq
2025-05-29 00:47:46,869 - src.vector_rag.pipeline_integration - INFO - ✅ Vector RAG integration enabled
2025-05-29 00:47:46,869 - src.vector_rag_pipeline - INFO - Vector RAG Enhanced Pipeline initialized (RAG enabled: True, OpenAI: True)
2025-05-29 00:47:46,869 - src.vector_rag_pipeline - INFO - 🚀 Starting Vector RAG enhanced extraction for: Jhajjar Power Plant
2025-05-29 00:47:46,869 - src.vector_rag_pipeline - INFO - 📊 Running full pipeline (no existing content or Vector RAG disabled)
2025-05-29 00:47:46,869 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-29 00:47:46,869 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-29 00:47:50,281 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-29 00:47:50,282 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-29 00:47:52,659 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-29 00:47:53,660 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:47:54,791 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:47:55,793 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-29 00:47:58,067 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-29 00:47:59,069 - src.simple_pipeline - INFO - Scraping 4/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-29 00:48:00,296 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-29 00:48:01,297 - src.simple_pipeline - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-29 00:48:03,471 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-29 00:48:04,473 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-29 00:48:04,473 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-29 00:48:04,474 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['other', 'wikipedia', 'company_official']}
2025-05-29 00:48:04,474 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-29 00:48:04,474 - src.groq_client - INFO - Starting LLM extraction for 5 content pieces
2025-05-29 00:48:04,703 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:48:04,705 - src.groq_client - ERROR - Groq extraction failed for field organization_name: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99415, Requested 1168. Please try again in 8m23.276s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:48:04,749 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:48:04,751 - src.groq_client - ERROR - Groq extraction failed for field cfpp_type: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99415, Requested 1246. Please try again in 9m30.62s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:48:04,751 - src.groq_client - INFO - Extracted cfpp_type: None (confidence: 0.00)
2025-05-29 00:48:05,324 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:48:05,325 - src.groq_client - ERROR - Groq extraction failed for field country_name: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99414, Requested 1134. Please try again in 7m53.277999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:48:05,326 - src.groq_client - INFO - Extracted country_name: None (confidence: 0.00)
2025-05-29 00:48:05,978 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:48:05,980 - src.groq_client - ERROR - Groq extraction failed for field province: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99414, Requested 1152. Please try again in 8m8.197s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:48:05,980 - src.groq_client - INFO - Extracted province: None (confidence: 0.00)
2025-05-29 00:48:06,528 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:48:06,530 - src.groq_client - ERROR - Groq extraction failed for field plants_count: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99413, Requested 1134. Please try again in 7m52.074s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:48:06,530 - src.groq_client - INFO - Extracted plants_count: None (confidence: 0.00)
2025-05-29 00:48:07,085 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:48:07,086 - src.groq_client - ERROR - Groq extraction failed for field plant_types: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99412, Requested 1153. Please try again in 8m7.933999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:48:07,087 - src.groq_client - INFO - Extracted plant_types: None (confidence: 0.00)
2025-05-29 00:48:07,643 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:48:07,644 - src.groq_client - ERROR - Groq extraction failed for field ppa_flag: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99412, Requested 1206. Please try again in 8m53.165s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:48:07,644 - src.groq_client - INFO - Extracted ppa_flag: None (confidence: 0.00)
2025-05-29 00:48:08,243 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:48:08,245 - src.groq_client - ERROR - Groq extraction failed for field currency_in: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99411, Requested 1158. Please try again in 8m11.103s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:48:08,246 - src.groq_client - INFO - Extracted currency_in: None (confidence: 0.00)
2025-05-29 00:48:08,805 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:48:08,806 - src.groq_client - ERROR - Groq extraction failed for field financial_year: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99410, Requested 1278. Please try again in 9m54.212s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:48:08,806 - src.groq_client - INFO - Extracted financial_year: None (confidence: 0.00)
2025-05-29 00:48:09,307 - src.groq_client - INFO - LLM extraction completed
2025-05-29 00:48:09,308 - src.simple_pipeline - INFO - ✅ Organizational extraction completed
2025-05-29 00:48:09,309 - src.simple_pipeline - INFO - 🔧 Step 4: Extracting plant details with smart field-by-field approach
2025-05-29 00:48:09,309 - src.simple_pipeline - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-29 00:48:09,309 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-29 00:48:09,410 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:48:09,411 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field name: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99410, Requested 1716. Please try again in 16m12.041999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:48:09,411 - src.plant_details_extractor - INFO - Extracted name: None (confidence: 0.00)
2025-05-29 00:48:09,986 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:48:09,987 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_type: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99409, Requested 1737. Please try again in 16m29.608s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:48:09,987 - src.plant_details_extractor - INFO - Extracted plant_type: None (confidence: 0.00)
2025-05-29 00:48:10,627 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:48:10,628 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_address: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99408, Requested 1730. Please try again in 16m22.955s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:48:10,628 - src.plant_details_extractor - INFO - Extracted plant_address: None (confidence: 0.00)
2025-05-29 00:48:11,192 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:48:11,193 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field lat: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99407, Requested 1736. Please try again in 16m27.539s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:48:11,194 - src.plant_details_extractor - INFO - Extracted lat: None (confidence: 0.00)
2025-05-29 00:48:11,758 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:48:11,759 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field long: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99407, Requested 1737. Please try again in 16m27.848s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:48:11,759 - src.plant_details_extractor - INFO - Extracted long: None (confidence: 0.00)
2025-05-29 00:48:12,311 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:48:12,312 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99406, Requested 1726. Please try again in 16m17.779s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:48:12,312 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-29 00:48:12,814 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-29 00:48:12,814 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "capacity"'
2025-05-29 00:48:12,814 - src.plant_details_extractor - INFO - Using input plant name as fallback: Jhajjar Power Plant
2025-05-29 00:48:12,814 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-29 00:48:12,814 - src.simple_pipeline - INFO - ✅ Field 'name' extracted from cache
2025-05-29 00:48:12,814 - src.simple_pipeline - INFO - ✅ Field 'plant_id' extracted from cache
2025-05-29 00:48:12,814 - src.simple_pipeline - INFO - 🔍 Field 'lat' is missing - will search specifically
2025-05-29 00:48:12,814 - src.simple_pipeline - INFO - 🔍 Field 'long' is missing - will search specifically
2025-05-29 00:48:12,814 - src.simple_pipeline - INFO - 🔍 Field 'plant_address' is missing - will search specifically
2025-05-29 00:48:12,814 - src.simple_pipeline - INFO - 🔍 Field 'grid_connectivity_maps' is missing - will search specifically
2025-05-29 00:48:12,815 - src.simple_pipeline - INFO - 🔍 Field 'ppa_details' is missing - will search specifically
2025-05-29 00:48:12,815 - src.simple_pipeline - INFO - 🔍 Field 'plant_type' is missing - will search specifically
2025-05-29 00:48:12,815 - src.simple_pipeline - INFO - 🔍 Field 'units_id' is missing - will search specifically
2025-05-29 00:48:12,815 - src.simple_pipeline - INFO - 🎯 Starting targeted searches for 7 missing fields
2025-05-29 00:48:12,815 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'lat' data
2025-05-29 00:48:12,815 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant latitude coordinates GPS location'
2025-05-29 00:48:16,000 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:48:17,610 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:48:18,612 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-29 00:48:25,522 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-29 00:48:26,524 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-29 00:48:27,810 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-29 00:48:28,812 - src.simple_pipeline - INFO - ✅ Found targeted content for 'lat' - 3 sources
2025-05-29 00:48:28,814 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'lat' using RAG: 28.607111
2025-05-29 00:48:28,815 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'long' data
2025-05-29 00:48:28,815 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant longitude coordinates GPS location'
