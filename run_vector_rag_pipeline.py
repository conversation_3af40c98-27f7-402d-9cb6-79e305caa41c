#!/usr/bin/env python3
"""
Vector RAG Enhanced Pipeline CLI

Simple command-line interface for running the Vector RAG enhanced power plant extraction pipeline.
This script provides the same interface as the existing Groq and OpenAI pipeline scripts.

Usage:
    python run_vector_rag_pipeline.py "Plant Name"

Examples:
    python run_vector_rag_pipeline.py "Jhajjar Power Plant"
    python run_vector_rag_pipeline.py "Adani Mundra Power Plant"
    python run_vector_rag_pipeline.py "NTPC Vindhyachal"
"""

import asyncio
import sys
import time
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.vector_rag_pipeline import VectorRAGPowerPlantPipeline


def setup_logging():
    """Configure logging for the pipeline."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('pipeline_vector_rag.log'),
            logging.StreamHandler()
        ]
    )


def print_banner():
    """Print the Vector RAG pipeline banner."""
    print("=" * 80)
    print("🧠 VECTOR RAG ENHANCED POWER PLANT EXTRACTION PIPELINE")
    print("=" * 80)
    print("🚀 Enhanced with semantic search and intelligent field extraction")
    print("📊 Combines traditional scraping with Vector RAG technology")
    print("⚡ Faster missing field extraction with higher accuracy")
    print("=" * 80)


def print_usage():
    """Print usage instructions."""
    print("\n📋 Usage:")
    print("  python run_vector_rag_pipeline.py \"Plant Name\" [options]")
    print("\n🔧 Options:")
    print("  --openai, -o        Use OpenAI instead of Groq (for rate limit issues)")
    print("  --existing-only, -e Use existing targeted content only (no new scraping)")
    print("  --help, -h          Show this help message")
    print("\n📝 Examples:")
    print("  # Full pipeline with Vector RAG enhancement (DEFAULT)")
    print("  python run_vector_rag_pipeline.py \"Adani Mundra Power Plant\"")
    print("")
    print("  # Use OpenAI if Groq rate limited")
    print("  python run_vector_rag_pipeline.py \"Jhajjar Power Plant\" --openai")
    print("")
    print("  # Use existing targeted content only (no new scraping)")
    print("  python run_vector_rag_pipeline.py \"Jhajjar Power Plant\" --existing-only")
    print("")
    print("  # Use OpenAI with existing content only")
    print("  python run_vector_rag_pipeline.py \"Jhajjar Power Plant\" --openai --existing-only")
    print("\n🎯 Features:")
    print("  ✅ Full pipeline: Search → Scrape → Extract → Create targeted_content → Vector RAG enhancement")
    print("  ✅ Vector RAG works on fresh targeted_content from current run")
    print("  ✅ Automatic LLM provider switching (Groq ↔ OpenAI)")
    print("  ✅ Semantic search for missing fields")
    print("  ✅ Automatic vector database creation and management")
    print("  ✅ Comprehensive extraction statistics")
    print("\n💡 Tips:")
    print("  - Default mode: Full pipeline + Vector RAG enhancement")
    print("  - Use --openai if you hit Groq rate limits")
    print("  - Use --existing-only only if you want to reuse old targeted_content files")


async def run_vector_rag_extraction(plant_name: str, use_openai: bool = False, use_existing_only: bool = False):
    """
    Run the Vector RAG enhanced extraction pipeline.

    Args:
        plant_name: Name of the power plant to extract
        use_openai: Whether to use OpenAI instead of Groq
        use_existing_only: Whether to use existing targeted content only
    """
    print(f"\n🎯 Target Plant: {plant_name}")
    print(f"🧠 Vector RAG: Enabled")
    print(f"📊 LLM Provider: {'OpenAI' if use_openai else 'Groq (with OpenAI fallback)'}")
    print(f"📁 Content Mode: {'Existing content only' if use_existing_only else 'Full pipeline + Vector RAG enhancement'}")

    start_time = time.time()

    try:
        # Create Vector RAG enhanced pipeline
        print("\n🔧 Initializing Vector RAG enhanced pipeline...")
        pipeline = VectorRAGPowerPlantPipeline(enable_vector_rag=True, use_openai=use_openai)

        # Get pipeline info
        rag_stats = pipeline.get_rag_stats()
        print(f"✅ Vector RAG enabled: {rag_stats['enabled']}")
        print(f"📊 Embedding model: {rag_stats['embedding_model']}")
        print(f"📁 Vector DB path: {rag_stats['vector_db_path']}")

        # Check for existing targeted content
        targeted_file = pipeline._find_targeted_content_file(plant_name)
        if targeted_file:
            print(f"📁 Found existing targeted content: {targeted_file}")
            if use_existing_only:
                print("🧠 Using Vector RAG-only mode (no new scraping)")
            else:
                print("📊 Will use existing content + full pipeline if needed")
        else:
            print("❌ No existing targeted content found")
            if use_existing_only:
                print("⚠️  Switching to full pipeline mode")
                use_existing_only = False

        # Run extraction
        print(f"\n🚀 Starting extraction for: {plant_name}")
        org_details, plant_details, extraction_info = await pipeline.extract_plant_data(
            plant_name,
            use_existing_content=use_existing_only
        )

        total_duration = time.time() - start_time

        # Display results
        print(f"\n✅ Extraction completed in {total_duration:.2f} seconds")
        print(f"📊 Strategy: {extraction_info.get('strategy', 'unknown')}")
        print(f"🧠 Vector RAG enabled: {extraction_info.get('vector_rag_enabled', False)}")

        # Show Vector RAG specific results
        if extraction_info.get('vector_rag_extractions'):
            rag_info = extraction_info['vector_rag_extractions']
            print(f"\n🎯 Vector RAG Results:")
            print(f"  - Attempted fields: {rag_info.get('attempted_fields', [])}")
            print(f"  - Successful extractions: {rag_info.get('successful_extractions', [])}")
            print(f"  - Failed extractions: {rag_info.get('failed_extractions', [])}")
            print(f"  - Vector DB created: {rag_info.get('vector_db_created', False)}")

            # Show performance improvements
            if rag_info.get('successful_extractions'):
                print(f"\n🚀 Performance Improvements:")
                for field in rag_info['successful_extractions']:
                    print(f"  ✅ {field}: Enhanced with Vector RAG")

        # Show extraction statistics
        if extraction_info.get('total_pages_scraped'):
            print(f"\n📊 Extraction Statistics:")
            print(f"  - Total pages scraped: {extraction_info.get('total_pages_scraped', 0)}")
            print(f"  - Missing field searches: {extraction_info.get('missing_field_searches', 0)}")
            print(f"  - Cache hit fields: {extraction_info.get('cache_hit_fields', [])}")

        # Save results with timestamp
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        await pipeline.save_results(
            org_details=org_details,
            plant_details=plant_details,
            extraction_info=extraction_info,
            org_output_path=f"vector_rag_org_{timestamp}.json",
            plant_output_path=f"vector_rag_plant_{timestamp}.json",
            info_output_path=f"vector_rag_info_{timestamp}.json"
        )

        print(f"\n💾 Results saved:")
        print(f"  - Organization: vector_rag_org_{timestamp}.json")
        print(f"  - Plant Details: vector_rag_plant_{timestamp}.json")
        print(f"  - Extraction Info: vector_rag_info_{timestamp}.json")

        # Show key extracted data
        if plant_details:
            print(f"\n📋 Key Extracted Data:")

            # Handle both PlantDetails objects and dictionaries
            if hasattr(plant_details, 'model_dump'):
                plant_data = plant_details.model_dump()
            elif isinstance(plant_details, dict):
                plant_data = plant_details
            else:
                plant_data = {}

            # Show important fields
            key_fields = ['lat', 'long', 'plant_address', 'plant_type']
            for field in key_fields:
                value = plant_data.get(field)
                if value and value != "" and value != []:
                    if isinstance(value, (int, float)):
                        print(f"  ✅ {field}: {value}")
                    else:
                        preview = str(value)[:60] + "..." if len(str(value)) > 60 else str(value)
                        print(f"  ✅ {field}: {preview}")
                else:
                    print(f"  ❌ {field}: Not found")

        print(f"\n🎉 Vector RAG enhanced extraction completed successfully!")
        print(f"📊 Total execution time: {total_duration:.2f} seconds")

        return True

    except Exception as e:
        print(f"\n❌ Extraction failed: {e}")
        logging.error(f"Vector RAG extraction failed for {plant_name}: {e}", exc_info=True)
        return False


def main():
    """Main CLI entry point."""
    setup_logging()
    print_banner()

    # Parse command line arguments
    use_openai = False
    use_existing_only = False  # Default to full pipeline with Vector RAG enhancement
    plant_name = None

    # Simple argument parsing
    args = sys.argv[1:]

    if not args or args[0].lower() in ['--help', '-h', 'help']:
        print_usage()
        sys.exit(0)

    # Parse flags and plant name
    for arg in args:
        if arg.lower() in ['--openai', '-o']:
            use_openai = True
        elif arg.lower() in ['--existing-only', '-e']:
            use_existing_only = True
        elif not plant_name:  # First non-flag argument is plant name
            plant_name = arg.strip()

    if not plant_name:
        print("❌ Error: Plant name is required")
        print_usage()
        sys.exit(1)

    # Show configuration
    print(f"🔧 Configuration:")
    print(f"  - Plant: {plant_name}")
    print(f"  - LLM Provider: {'OpenAI' if use_openai else 'Groq (with OpenAI fallback)'}")
    print(f"  - Mode: {'Existing content only' if use_existing_only else 'Full pipeline + Vector RAG enhancement'}")

    # Run the extraction
    try:
        success = asyncio.run(run_vector_rag_extraction(plant_name, use_openai, use_existing_only))

        if success:
            print(f"\n🎉 SUCCESS: Vector RAG extraction completed for '{plant_name}'")
            print(f"📁 Check the generated JSON files for detailed results")
            print(f"📊 Check 'pipeline_vector_rag.log' for execution logs")
            sys.exit(0)
        else:
            print(f"\n❌ FAILED: Vector RAG extraction failed for '{plant_name}'")
            print(f"📊 Check 'pipeline_vector_rag.log' for error details")
            sys.exit(1)

    except KeyboardInterrupt:
        print(f"\n⚠️  Extraction interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        logging.error(f"Unexpected error in main: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
