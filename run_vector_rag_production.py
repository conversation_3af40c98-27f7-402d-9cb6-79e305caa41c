#!/usr/bin/env python3
"""
Production Vector RAG Enhanced Pipeline.

This script demonstrates real-world usage of the Vector RAG enhanced pipeline
with your existing LLM clients and actual power plant data.
"""

import asyncio
import logging
import time
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.vector_rag_pipeline import VectorRAGPowerPlantPipeline
from src.config import config


def setup_logging():
    """Configure logging for production use."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('vector_rag_production.log'),
            logging.StreamHandler()
        ]
    )


async def test_with_existing_targeted_content():
    """Test Vector RAG with existing targeted_content file."""
    print("\n🧪 Testing Vector RAG with Existing Targeted Content")
    print("=" * 60)

    # Find existing targeted_content file
    targeted_content_file = "jhajjar_power_plant_targeted_content_20250528_232641.json"

    if not os.path.exists(targeted_content_file):
        print(f"❌ Targeted content file not found: {targeted_content_file}")
        print("Please run the main pipeline first to generate targeted content")
        return False

    print(f"📁 Using existing file: {targeted_content_file}")

    try:
        # Create Vector RAG enhanced pipeline
        print("🔧 Initializing Vector RAG enhanced pipeline...")
        pipeline = VectorRAGPowerPlantPipeline(enable_vector_rag=True)

        # Get RAG system info
        rag_stats = pipeline.get_rag_stats()
        print(f"✅ Vector RAG enabled: {rag_stats['enabled']}")
        print(f"📊 Embedding model: {rag_stats['embedding_model']}")

        # Test with Jhajjar Power Plant
        plant_name = "Jhajjar Power Plant"
        print(f"\n🚀 Starting Vector RAG enhanced extraction for: {plant_name}")

        start_time = time.time()

        # Extract with Vector RAG enhancement
        org_details, plant_details, extraction_info = await pipeline.extract_plant_data(plant_name)

        total_duration = time.time() - start_time

        # Display results
        print(f"\n✅ Extraction completed in {total_duration:.2f} seconds")
        print(f"📊 Strategy: {extraction_info.get('strategy', 'unknown')}")
        print(f"🧠 Vector RAG enabled: {extraction_info.get('vector_rag_enabled', False)}")

        # Show Vector RAG specific results
        if extraction_info.get('vector_rag_extractions'):
            rag_info = extraction_info['vector_rag_extractions']
            print(f"\n🎯 Vector RAG Results:")
            print(f"  - Attempted fields: {rag_info.get('attempted_fields', [])}")
            print(f"  - Successful extractions: {rag_info.get('successful_extractions', [])}")
            print(f"  - Failed extractions: {rag_info.get('failed_extractions', [])}")
            print(f"  - Vector DB created: {rag_info.get('vector_db_created', False)}")

        # Show plant details improvements
        if plant_details:
            # Handle both PlantDetails objects and dictionaries
            if hasattr(plant_details, 'model_dump'):
                plant_data = plant_details.model_dump()
            elif isinstance(plant_details, dict):
                plant_data = plant_details
            else:
                plant_data = {}

            print(f"\n📋 Enhanced Plant Details:")

            enhanced_fields = extraction_info.get('vector_rag_extractions', {}).get('successful_extractions', [])
            for field_name in ['lat', 'long', 'grid_connectivity_maps', 'ppa_details', 'units_id']:
                value = plant_data.get(field_name)
                enhancement_status = "🧠 RAG Enhanced" if field_name in enhanced_fields else "📊 Standard"

                if value and value != "" and value != []:
                    if isinstance(value, list):
                        print(f"  - {field_name}: {len(value)} items {enhancement_status}")
                    else:
                        preview = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                        print(f"  - {field_name}: {preview} {enhancement_status}")
                else:
                    print(f"  - {field_name}: Not found")

        # Save enhanced results
        await pipeline.save_results(
            org_details=org_details,
            plant_details=plant_details,
            extraction_info=extraction_info,
            org_output_path="production_org_details_vector_rag.json",
            plant_output_path="production_plant_details_vector_rag.json",
            info_output_path="production_extraction_info_vector_rag.json"
        )

        print(f"\n💾 Results saved with 'production_' prefix")
        print(f"📊 Vector RAG statistics saved to vector_rag_stats.json")

        return True

    except Exception as e:
        print(f"❌ Production test failed: {e}")
        logging.error(f"Production test failed: {e}", exc_info=True)
        return False


async def run_full_pipeline_with_vector_rag():
    """Run the complete pipeline with Vector RAG enhancement."""
    print("\n🚀 Running Full Pipeline with Vector RAG Enhancement")
    print("=" * 60)

    plant_name = "Jhajjar Power Plant"

    try:
        # Create Vector RAG enhanced pipeline
        print("🔧 Initializing Vector RAG enhanced pipeline...")
        pipeline = VectorRAGPowerPlantPipeline(enable_vector_rag=True)

        print(f"🎯 Target plant: {plant_name}")
        print(f"🧠 Vector RAG: Enabled")
        print(f"📊 LLM Client: {type(pipeline.plant_extractor.llm_client).__name__}")

        start_time = time.time()

        # Run complete extraction
        org_details, plant_details, extraction_info = await pipeline.extract_plant_data(plant_name)

        total_duration = time.time() - start_time

        # Display comprehensive results
        print(f"\n✅ Complete pipeline finished in {total_duration:.2f} seconds")

        # Show extraction strategy breakdown
        print(f"\n📊 Extraction Strategy Breakdown:")
        print(f"  - Strategy: {extraction_info.get('strategy', 'unknown')}")
        print(f"  - Initial search time: {extraction_info.get('initial_search_time', 0):.2f}s")
        print(f"  - Missing field searches: {extraction_info.get('missing_field_searches', 0)}")
        print(f"  - Total pages scraped: {extraction_info.get('total_pages_scraped', 0)}")
        print(f"  - Cache hit fields: {extraction_info.get('cache_hit_fields', [])}")

        # Show Vector RAG impact
        if extraction_info.get('vector_rag_extractions'):
            rag_info = extraction_info['vector_rag_extractions']
            print(f"\n🧠 Vector RAG Impact:")
            print(f"  - Fields enhanced by RAG: {len(rag_info.get('successful_extractions', []))}")
            print(f"  - RAG success rate: {len(rag_info.get('successful_extractions', [])) / max(1, len(rag_info.get('attempted_fields', []))) * 100:.1f}%")

            if rag_info.get('successful_extractions'):
                print(f"  - Successfully enhanced: {', '.join(rag_info['successful_extractions'])}")

        # Save results with timestamp
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        await pipeline.save_results(
            org_details=org_details,
            plant_details=plant_details,
            extraction_info=extraction_info,
            org_output_path=f"full_pipeline_org_{timestamp}.json",
            plant_output_path=f"full_pipeline_plant_{timestamp}.json",
            info_output_path=f"full_pipeline_info_{timestamp}.json"
        )

        print(f"\n💾 Complete results saved with timestamp: {timestamp}")

        return True

    except Exception as e:
        print(f"❌ Full pipeline failed: {e}")
        logging.error(f"Full pipeline failed: {e}", exc_info=True)
        return False


async def compare_traditional_vs_vector_rag():
    """Compare traditional pipeline vs Vector RAG enhanced pipeline."""
    print("\n⚖️  Comparing Traditional vs Vector RAG Enhanced Pipeline")
    print("=" * 60)

    plant_name = "Jhajjar Power Plant"

    try:
        # Test 1: Traditional pipeline (RAG disabled)
        print("📊 Running traditional pipeline (Vector RAG disabled)...")
        traditional_start = time.time()

        traditional_pipeline = VectorRAGPowerPlantPipeline(enable_vector_rag=False)
        trad_org, trad_plant, trad_info = await traditional_pipeline.extract_plant_data(plant_name)

        traditional_duration = time.time() - traditional_start

        # Test 2: Vector RAG enhanced pipeline
        print("🧠 Running Vector RAG enhanced pipeline...")
        rag_start = time.time()

        rag_pipeline = VectorRAGPowerPlantPipeline(enable_vector_rag=True)
        rag_org, rag_plant, rag_info = await rag_pipeline.extract_plant_data(plant_name)

        rag_duration = time.time() - rag_start

        # Compare results
        print(f"\n📊 Performance Comparison:")
        print(f"  Traditional duration: {traditional_duration:.2f}s")
        print(f"  Vector RAG duration: {rag_duration:.2f}s")
        print(f"  Speed difference: {((traditional_duration - rag_duration) / traditional_duration * 100):+.1f}%")

        # Compare field completeness
        print(f"\n🔍 Field Completeness Comparison:")

        trad_data = trad_plant.model_dump() if trad_plant else {}
        rag_data = rag_plant.model_dump() if rag_plant else {}

        comparison_fields = ['lat', 'long', 'grid_connectivity_maps', 'ppa_details', 'units_id', 'plant_address']

        improvements = 0
        for field in comparison_fields:
            trad_value = trad_data.get(field)
            rag_value = rag_data.get(field)

            trad_complete = bool(trad_value and trad_value != "" and trad_value != [])
            rag_complete = bool(rag_value and rag_value != "" and rag_value != [])

            if rag_complete and not trad_complete:
                status = "🟢 RAG Improved"
                improvements += 1
            elif trad_complete and rag_complete:
                status = "✅ Both Complete"
            elif trad_complete and not rag_complete:
                status = "🔴 RAG Regression"
            else:
                status = "⚪ Both Missing"

            print(f"  {field}: {status}")

        print(f"\n🎯 Summary: Vector RAG improved {improvements} fields")

        return True

    except Exception as e:
        print(f"❌ Comparison failed: {e}")
        logging.error(f"Comparison failed: {e}", exc_info=True)
        return False


async def main():
    """Run production Vector RAG tests."""
    setup_logging()

    print("🚀 Vector RAG Production Testing")
    print("=" * 80)
    print(f"🔧 Using Groq API Key: {'✅ Available' if config.pipeline.groq_api_key else '❌ Missing'}")
    print(f"🔧 Using OpenAI API Key: {'✅ Available' if hasattr(config.pipeline, 'openai_api_key') and config.pipeline.openai_api_key else '❌ Missing'}")

    # Test scenarios
    test_scenarios = [
        ("Existing Targeted Content Test", test_with_existing_targeted_content),
        ("Full Pipeline with Vector RAG", run_full_pipeline_with_vector_rag),
        ("Traditional vs Vector RAG Comparison", compare_traditional_vs_vector_rag)
    ]

    results = []

    for name, test_func in test_scenarios:
        print(f"\n{'='*20} {name} {'='*20}")
        try:
            success = await test_func()
            results.append((name, success))
            if success:
                print(f"✅ {name} completed successfully")
            else:
                print(f"❌ {name} failed")
        except Exception as e:
            print(f"❌ {name} failed with error: {e}")
            results.append((name, False))

    # Final summary
    print("\n" + "=" * 80)
    print("📊 Production Testing Summary:")
    successful_tests = sum(1 for _, success in results if success)
    total_tests = len(results)

    for name, success in results:
        status = "✅" if success else "❌"
        print(f"  {status} {name}")

    print(f"\n🎯 Overall Success Rate: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.1f}%)")
    print(f"📁 Detailed logs saved to: vector_rag_production.log")

    if successful_tests == total_tests:
        print("\n🎉 All tests passed! Vector RAG is ready for production use!")
    else:
        print(f"\n⚠️  {total_tests - successful_tests} test(s) failed. Check logs for details.")


if __name__ == "__main__":
    asyncio.run(main())
