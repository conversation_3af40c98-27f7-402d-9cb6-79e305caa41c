"""
Field-specific content chunking strategies for vector database creation.

Different field types require different chunking approaches to optimize
semantic search and extraction accuracy.
"""

import re
import logging
from typing import List, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ChunkConfig:
    """Configuration for field-specific chunking."""
    chunk_size: int
    overlap: int
    preserve_patterns: List[str]
    split_patterns: List[str]


class FieldSpecificChunker:
    """Handles field-specific content chunking for optimal vector search."""
    
    def __init__(self):
        """Initialize with field-specific chunking configurations."""
        self.field_configs = {
            "lat": ChunkConfig(
                chunk_size=300,
                overlap=50,
                preserve_patterns=[
                    r'\d+\.\d+',  # Decimal coordinates
                    r'\d+°\s*\d+\'?\s*\d*\.?\d*"?\s*[NS]',  # DMS coordinates
                    r'latitude|longitude|coordinates|GPS'
                ],
                split_patterns=[r'\n\n', r'\.', r';']
            ),
            "long": ChunkConfig(
                chunk_size=300,
                overlap=50,
                preserve_patterns=[
                    r'\d+\.\d+',  # Decimal coordinates
                    r'\d+°\s*\d+\'?\s*\d*\.?\d*"?\s*[EW]',  # DMS coordinates
                    r'latitude|longitude|coordinates|GPS'
                ],
                split_patterns=[r'\n\n', r'\.', r';']
            ),
            "grid_connectivity_maps": ChunkConfig(
                chunk_size=1000,
                overlap=100,
                preserve_patterns=[
                    r'\d+\s*kV',  # Voltage levels
                    r'substation|transmission|grid|switchyard',
                    r'HVPNL|HPGCL|transmission lines',
                    r'\d+\s*km',  # Distances
                    r'Sonipat|Mahendragarh|substations'
                ],
                split_patterns=[r'\n\n', r'\. ', r';', r'Table \d+']
            ),
            "ppa_details": ChunkConfig(
                chunk_size=1500,
                overlap=150,
                preserve_patterns=[
                    r'power purchase agreement|PPA',
                    r'\d+\s*MW',  # Capacity
                    r'\d+\s*years?|tenure',
                    r'contract|agreement|pricing',
                    r'Haryana|distribution|utility'
                ],
                split_patterns=[r'\n\n', r'\. ', r';', r'Table \d+']
            )
        }
    
    def chunk_content_for_field(self, field_name: str, content: str) -> List[str]:
        """
        Chunk content based on field-specific requirements.
        
        Args:
            field_name: The field type (lat, long, grid_connectivity_maps, etc.)
            content: The raw content to chunk
            
        Returns:
            List of content chunks optimized for the field type
        """
        if field_name not in self.field_configs:
            logger.warning(f"No specific chunking config for field '{field_name}', using default")
            return self._chunk_default(content)
        
        config = self.field_configs[field_name]
        
        if field_name in ["lat", "long"]:
            return self._chunk_coordinate_content(content, config)
        elif field_name == "grid_connectivity_maps":
            return self._chunk_technical_content(content, config)
        elif field_name == "ppa_details":
            return self._chunk_contract_content(content, config)
        else:
            return self._chunk_with_config(content, config)
    
    def _chunk_coordinate_content(self, content: str, config: ChunkConfig) -> List[str]:
        """Chunk content for coordinate fields with focus on location data."""
        chunks = []
        
        # First, extract sections that likely contain coordinates
        coordinate_sections = []
        
        # Look for coordinate patterns and extract surrounding context
        for pattern in config.preserve_patterns:
            matches = list(re.finditer(pattern, content, re.IGNORECASE))
            for match in matches:
                start = max(0, match.start() - 150)
                end = min(len(content), match.end() + 150)
                section = content[start:end].strip()
                if section and len(section) > 50:
                    coordinate_sections.append(section)
        
        # If we found coordinate sections, use them
        if coordinate_sections:
            chunks.extend(coordinate_sections)
        
        # Also do regular chunking to catch any missed content
        regular_chunks = self._chunk_with_config(content, config)
        chunks.extend(regular_chunks)
        
        # Remove duplicates and very short chunks
        unique_chunks = []
        seen = set()
        for chunk in chunks:
            chunk_clean = chunk.strip()
            if len(chunk_clean) > 30 and chunk_clean not in seen:
                unique_chunks.append(chunk_clean)
                seen.add(chunk_clean)
        
        return unique_chunks[:10]  # Limit to top 10 chunks for coordinates
    
    def _chunk_technical_content(self, content: str, config: ChunkConfig) -> List[str]:
        """Chunk technical content preserving grid connectivity context."""
        # Split on major section breaks first
        major_sections = re.split(r'\n\n+', content)
        
        chunks = []
        for section in major_sections:
            if len(section.strip()) < 100:
                continue
                
            # Check if section contains technical terms
            has_technical_content = any(
                re.search(pattern, section, re.IGNORECASE) 
                for pattern in config.preserve_patterns
            )
            
            if has_technical_content:
                # For technical sections, use larger chunks to preserve context
                section_chunks = self._split_with_overlap(
                    section, config.chunk_size, config.overlap
                )
                chunks.extend(section_chunks)
            else:
                # For non-technical sections, use smaller chunks
                section_chunks = self._split_with_overlap(
                    section, config.chunk_size // 2, config.overlap
                )
                chunks.extend(section_chunks)
        
        return [chunk.strip() for chunk in chunks if len(chunk.strip()) > 100]
    
    def _chunk_contract_content(self, content: str, config: ChunkConfig) -> List[str]:
        """Chunk contract content preserving commercial context."""
        # Look for contract-specific sections
        contract_sections = []
        
        # Split by major breaks and identify contract-relevant sections
        sections = re.split(r'\n\n+', content)
        
        for section in sections:
            if len(section.strip()) < 150:
                continue
                
            # Check if section contains contract terms
            has_contract_content = any(
                re.search(pattern, section, re.IGNORECASE)
                for pattern in config.preserve_patterns
            )
            
            if has_contract_content:
                # Use larger chunks for contract sections
                section_chunks = self._split_with_overlap(
                    section, config.chunk_size, config.overlap
                )
                contract_sections.extend(section_chunks)
        
        # If no contract sections found, fall back to regular chunking
        if not contract_sections:
            contract_sections = self._chunk_with_config(content, config)
        
        return [chunk.strip() for chunk in contract_sections if len(chunk.strip()) > 150]
    
    def _chunk_with_config(self, content: str, config: ChunkConfig) -> List[str]:
        """Generic chunking with configuration."""
        return self._split_with_overlap(content, config.chunk_size, config.overlap)
    
    def _chunk_default(self, content: str) -> List[str]:
        """Default chunking strategy."""
        return self._split_with_overlap(content, 800, 80)
    
    def _split_with_overlap(self, text: str, chunk_size: int, overlap: int) -> List[str]:
        """Split text into overlapping chunks."""
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # Try to break at sentence boundary
            if end < len(text):
                # Look for sentence break within last 100 characters
                last_period = text.rfind('.', end - 100, end)
                last_newline = text.rfind('\n', end - 100, end)
                
                break_point = max(last_period, last_newline)
                if break_point > start + chunk_size // 2:
                    end = break_point + 1
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end - overlap
            
            # Prevent infinite loop
            if start >= len(text):
                break
        
        return chunks
