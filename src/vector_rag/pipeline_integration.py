"""
Pipeline integration for Vector RAG functionality.

This module provides integration between the Vector RAG system and 
the existing power plant extraction pipelines.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
import asyncio

from .vectorizer import TargetedContentVectorizer
from .rag_extractor import VectorRAGExtractor

logger = logging.getLogger(__name__)


class VectorRAGPipelineIntegration:
    """Integrates Vector RAG capabilities into existing pipelines."""
    
    def __init__(
        self, 
        llm_client=None,
        vector_db_base_path: str = "vector_db",
        embedding_model: str = "all-MiniLM-L6-v2"
    ):
        """
        Initialize the Vector RAG pipeline integration.
        
        Args:
            llm_client: OpenAI or Groq client for extraction
            vector_db_base_path: Base directory for vector databases
            embedding_model: Sentence transformer model name
        """
        self.llm_client = llm_client
        self.vector_db_base_path = vector_db_base_path
        self.embedding_model = embedding_model
        self.vectorizer = None
        self.rag_extractor = None
        
        # Ensure vector DB directory exists
        os.makedirs(vector_db_base_path, exist_ok=True)
    
    async def process_targeted_content_with_rag(
        self,
        targeted_content_file: str,
        plant_name: str,
        missing_fields: List[str]
    ) -> Dict[str, Any]:
        """
        Process targeted content file and extract missing fields using RAG.
        
        Args:
            targeted_content_file: Path to the targeted_content JSON file
            plant_name: Name of the power plant
            missing_fields: List of field names to extract
            
        Returns:
            Dictionary with extracted field values
        """
        logger.info(f"🚀 Starting Vector RAG processing for {plant_name}")
        logger.info(f"📁 Targeted content file: {targeted_content_file}")
        logger.info(f"🎯 Missing fields to extract: {missing_fields}")
        
        # Step 1: Create vector database from targeted content
        vector_info = await self._create_vector_database(targeted_content_file)
        
        if not vector_info:
            logger.error("Failed to create vector database")
            return {}
        
        # Step 2: Extract fields using RAG
        extracted_fields = await self._extract_fields_with_rag(
            vector_info, plant_name, missing_fields
        )
        
        logger.info(f"✅ Vector RAG processing completed. Extracted {len(extracted_fields)} fields")
        return extracted_fields
    
    async def _create_vector_database(self, targeted_content_file: str) -> Optional[Dict]:
        """Create vector database from targeted content file."""
        
        try:
            # Initialize vectorizer if not already done
            if not self.vectorizer:
                self.vectorizer = TargetedContentVectorizer(self.embedding_model)
            
            # Build vector databases
            logger.info("🔨 Creating vector databases from targeted content...")
            vector_info = await self.vectorizer.build_vector_db_from_targeted_content(
                targeted_content_file,
                output_dir=self.vector_db_base_path
            )
            
            logger.info(f"✅ Vector databases created: {list(vector_info['fields'].keys())}")
            return vector_info
            
        except Exception as e:
            logger.error(f"Error creating vector database: {e}")
            return None
    
    async def _extract_fields_with_rag(
        self,
        vector_info: Dict,
        plant_name: str,
        missing_fields: List[str]
    ) -> Dict[str, Any]:
        """Extract missing fields using RAG."""
        
        extracted_fields = {}
        vector_db_path = vector_info["vector_db_path"]
        available_fields = list(vector_info["fields"].keys())
        
        # Initialize RAG extractor
        if not self.rag_extractor:
            self.rag_extractor = VectorRAGExtractor(
                vector_db_path=vector_db_path,
                embedding_model=self.embedding_model,
                llm_client=self.llm_client
            )
        
        # Extract each missing field that has a vector store
        for field_name in missing_fields:
            if field_name in available_fields:
                logger.info(f"🔍 Extracting field '{field_name}' using Vector RAG...")
                
                try:
                    extracted_value = await self.rag_extractor.extract_field_with_rag(
                        field_name=field_name,
                        plant_name=plant_name,
                        top_k=3,
                        score_threshold=0.5
                    )
                    
                    if extracted_value is not None:
                        extracted_fields[field_name] = extracted_value
                        logger.info(f"✅ Successfully extracted {field_name}")
                    else:
                        logger.warning(f"❌ No value extracted for {field_name}")
                        
                except Exception as e:
                    logger.error(f"Error extracting {field_name}: {e}")
                    continue
            else:
                logger.warning(f"No vector store available for field: {field_name}")
        
        return extracted_fields
    
    def get_vector_db_path_for_plant(self, plant_name: str, timestamp: str) -> str:
        """Get the vector database path for a specific plant."""
        plant_dir = f"{plant_name.lower().replace(' ', '_')}_{timestamp}"
        return os.path.join(self.vector_db_base_path, plant_dir)
    
    def vector_db_exists_for_plant(self, plant_name: str, timestamp: str) -> bool:
        """Check if vector database exists for a plant."""
        vector_db_path = self.get_vector_db_path_for_plant(plant_name, timestamp)
        metadata_file = os.path.join(vector_db_path, "metadata.json")
        return os.path.exists(metadata_file)
    
    async def load_existing_vector_db(self, plant_name: str, timestamp: str) -> Optional[VectorRAGExtractor]:
        """Load existing vector database for a plant."""
        
        vector_db_path = self.get_vector_db_path_for_plant(plant_name, timestamp)
        
        if not self.vector_db_exists_for_plant(plant_name, timestamp):
            logger.warning(f"No existing vector DB found for {plant_name} at {vector_db_path}")
            return None
        
        try:
            rag_extractor = VectorRAGExtractor(
                vector_db_path=vector_db_path,
                embedding_model=self.embedding_model,
                llm_client=self.llm_client
            )
            
            logger.info(f"✅ Loaded existing vector DB for {plant_name}")
            return rag_extractor
            
        except Exception as e:
            logger.error(f"Error loading existing vector DB: {e}")
            return None


class VectorRAGEnhancedPipeline:
    """Base class for pipelines enhanced with Vector RAG capabilities."""
    
    def __init__(self, llm_client=None, enable_vector_rag: bool = True):
        """
        Initialize enhanced pipeline.
        
        Args:
            llm_client: OpenAI or Groq client
            enable_vector_rag: Whether to enable Vector RAG functionality
        """
        self.llm_client = llm_client
        self.enable_vector_rag = enable_vector_rag
        
        if self.enable_vector_rag:
            try:
                self.vector_rag = VectorRAGPipelineIntegration(llm_client=llm_client)
                logger.info("✅ Vector RAG integration enabled")
            except ImportError as e:
                logger.warning(f"Vector RAG dependencies not available: {e}")
                self.enable_vector_rag = False
                self.vector_rag = None
        else:
            self.vector_rag = None
    
    async def enhance_extraction_with_rag(
        self,
        plant_data: Dict,
        targeted_content_file: str,
        plant_name: str,
        missing_fields: List[str]
    ) -> Dict:
        """
        Enhance extraction results using Vector RAG.
        
        Args:
            plant_data: Current plant data dictionary
            targeted_content_file: Path to targeted content JSON
            plant_name: Name of the plant
            missing_fields: Fields that need extraction
            
        Returns:
            Enhanced plant data with RAG-extracted fields
        """
        if not self.enable_vector_rag or not self.vector_rag:
            logger.info("Vector RAG not enabled, skipping enhancement")
            return plant_data
        
        if not missing_fields:
            logger.info("No missing fields, skipping RAG extraction")
            return plant_data
        
        if not os.path.exists(targeted_content_file):
            logger.warning(f"Targeted content file not found: {targeted_content_file}")
            return plant_data
        
        try:
            # Extract missing fields using Vector RAG
            rag_extracted = await self.vector_rag.process_targeted_content_with_rag(
                targeted_content_file=targeted_content_file,
                plant_name=plant_name,
                missing_fields=missing_fields
            )
            
            # Merge RAG results with existing plant data
            for field_name, value in rag_extracted.items():
                if value is not None and (field_name not in plant_data or not plant_data[field_name]):
                    plant_data[field_name] = value
                    logger.info(f"🎯 Enhanced {field_name} with Vector RAG")
            
            return plant_data
            
        except Exception as e:
            logger.error(f"Error in Vector RAG enhancement: {e}")
            return plant_data
    
    def get_rag_stats(self) -> Dict[str, Any]:
        """Get statistics about Vector RAG usage."""
        if not self.vector_rag:
            return {"enabled": False}
        
        return {
            "enabled": True,
            "vector_db_path": self.vector_rag.vector_db_base_path,
            "embedding_model": self.vector_rag.embedding_model
        }
