"""
RAG-based field extraction using vector similarity search.

This module provides extraction capabilities using vector databases
created from targeted content, with fallback to traditional methods.
"""

import json
import logging
from typing import Dict, List, Optional, Any, Tuple
import asyncio

try:
    import faiss
    import numpy as np
    from sentence_transformers import SentenceTransformer
    VECTOR_DEPS_AVAILABLE = True
except ImportError:
    VECTOR_DEPS_AVAILABLE = False
    faiss = None
    np = None
    SentenceTransformer = None

from .vectorizer import TargetedContentVectorizer

logger = logging.getLogger(__name__)


class VectorRAGExtractor:
    """Extract fields using vector similarity search on targeted content."""

    def __init__(
        self,
        vector_db_path: str,
        embedding_model: str = "all-MiniLM-L6-v2",
        llm_client = None
    ):
        """
        Initialize the RAG extractor.

        Args:
            vector_db_path: Path to the vector database directory
            embedding_model: Name of the sentence transformer model
            llm_client: LLM client for extraction (OpenAI or Groq)
        """
        if not VECTOR_DEPS_AVAILABLE:
            raise ImportError(
                "Vector dependencies not available. Please install: "
                "pip install faiss-cpu sentence-transformers numpy"
            )

        self.vector_db_path = vector_db_path
        self.embedding_model_name = embedding_model
        self.embedding_model = None  # Lazy load
        self.llm_client = llm_client

        # Lazy-loaded field vector stores
        self.field_stores = {}

        # Load metadata
        self.metadata = self._load_metadata()

    def _load_metadata(self) -> Optional[Dict]:
        """Load vector database metadata."""
        try:
            metadata_file = f"{self.vector_db_path}/metadata.json"
            with open(metadata_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading metadata: {e}")
            return None

    def _load_embedding_model(self):
        """Lazy load the embedding model."""
        if self.embedding_model is None:
            logger.info(f"Loading embedding model: {self.embedding_model_name}")
            self.embedding_model = SentenceTransformer(self.embedding_model_name)

    def _get_field_vector_store(self, field_name: str) -> Optional[Dict]:
        """Load vector store for a specific field (with caching)."""
        if field_name in self.field_stores:
            return self.field_stores[field_name]

        if not self.metadata or field_name not in self.metadata.get("fields", {}):
            logger.warning(f"No vector store available for field: {field_name}")
            return None

        # Load the vector store
        vectorizer = TargetedContentVectorizer()
        vector_store = vectorizer.load_field_vector_store(self.vector_db_path, field_name)

        if vector_store:
            self.field_stores[field_name] = vector_store
            logger.info(f"Loaded vector store for field: {field_name}")

        return vector_store

    async def extract_field_with_rag(
        self,
        field_name: str,
        plant_name: str,
        top_k: int = 3,
        score_threshold: float = 0.7
    ) -> Optional[Any]:
        """
        Extract field using vector similarity search + LLM.

        Args:
            field_name: The field to extract (e.g., "grid_connectivity_maps")
            plant_name: Plant name for context
            top_k: Number of top similar chunks to retrieve
            score_threshold: Minimum similarity score threshold

        Returns:
            Extracted field value or None if extraction fails
        """
        logger.info(f"Starting RAG extraction for field: {field_name}")

        # Handle complex nested fields with granular extraction
        if field_name in ["grid_connectivity_maps", "ppa_details"]:
            return await self._extract_complex_field_granular(field_name, plant_name, top_k, score_threshold)

        # Get vector store for this field
        vector_store = self._get_field_vector_store(field_name)
        if not vector_store:
            logger.warning(f"No vector store for field {field_name}, cannot use RAG")
            return None

        # Construct field-specific query
        query = self._construct_field_query(field_name, plant_name)
        logger.info(f"RAG query: '{query}'")

        # Use lower threshold for plant_address since it might have lower similarity
        adjusted_threshold = 0.3 if field_name == "plant_address" else score_threshold

        # Perform vector similarity search
        relevant_chunks = self._vector_similarity_search(
            vector_store, query, top_k, adjusted_threshold
        )

        if not relevant_chunks:
            logger.warning(f"No relevant chunks found for field {field_name} (threshold: {adjusted_threshold})")

            # Special handling for plant_address - try using lat/long content if available
            if field_name == "plant_address":
                logger.info("Trying to extract plant_address from lat/long content as fallback")
                lat_vector_store = self._get_field_vector_store("lat")
                if lat_vector_store:
                    # Use a more general address query on lat content
                    address_query = f"{plant_name} location address village district state"
                    lat_chunks = self._vector_similarity_search(lat_vector_store, address_query, top_k, 0.2)
                    if lat_chunks:
                        logger.info(f"Found {len(lat_chunks)} chunks from lat field for address extraction")
                        return await self._extract_with_llm(field_name, lat_chunks, plant_name)

            return None

        logger.info(f"Found {len(relevant_chunks)} relevant chunks for {field_name}")

        # Extract using LLM with retrieved content
        return await self._extract_with_llm(field_name, relevant_chunks, plant_name)

    async def _extract_complex_field_granular(
        self,
        field_name: str,
        plant_name: str,
        top_k: int = 3,
        score_threshold: float = 0.5  # Lower threshold for granular searches
    ) -> Optional[Any]:
        """
        Extract complex nested fields by breaking them into granular searches.

        Args:
            field_name: Complex field name (grid_connectivity_maps or ppa_details)
            plant_name: Plant name for context
            top_k: Number of chunks to retrieve per sub-field
            score_threshold: Similarity threshold for sub-field searches

        Returns:
            Structured data for the complex field
        """
        logger.info(f"🔍 Starting granular extraction for complex field: {field_name}")

        if field_name == "grid_connectivity_maps":
            return await self._extract_grid_connectivity_granular(plant_name, top_k, score_threshold)
        elif field_name == "ppa_details":
            return await self._extract_ppa_details_granular(plant_name, top_k, score_threshold)
        else:
            logger.warning(f"Unknown complex field: {field_name}")
            return None

    async def _extract_grid_connectivity_granular(
        self,
        plant_name: str,
        top_k: int,
        score_threshold: float
    ) -> List[Dict]:
        """Extract grid connectivity information using granular field searches."""

        logger.info("🔌 Extracting grid connectivity with granular approach")

        # Get the vector store for grid_connectivity_maps
        vector_store = self._get_field_vector_store("grid_connectivity_maps")
        if not vector_store:
            logger.warning("No vector store for grid_connectivity_maps")
            return []

        # Define granular searches for grid connectivity components
        granular_searches = {
            "substation_names": f"{plant_name} substation transmission switchyard grid connection",
            "voltage_levels": f"{plant_name} voltage kV transmission line electrical",
            "transmission_lines": f"{plant_name} transmission line power evacuation grid",
            "grid_operator": f"{plant_name} HVPNL grid operator transmission utility",
            "connection_points": f"{plant_name} grid tie-in connection point substation"
        }

        extracted_data = {}

        # Extract each component separately
        for component, query in granular_searches.items():
            logger.info(f"🔍 Searching for {component}: '{query}'")

            # Perform vector search for this component
            relevant_chunks = self._vector_similarity_search(
                vector_store, query, top_k, score_threshold
            )

            if relevant_chunks:
                logger.info(f"Found {len(relevant_chunks)} chunks for {component}")

                # Extract this component using LLM
                component_value = await self._extract_grid_component(
                    component, relevant_chunks, plant_name
                )

                if component_value:
                    extracted_data[component] = component_value
                    logger.info(f"✅ Extracted {component}: {component_value}")
            else:
                logger.info(f"❌ No chunks found for {component}")

        # Combine extracted components into structured format
        return self._build_grid_connectivity_structure(extracted_data)

    async def _extract_ppa_details_granular(
        self,
        plant_name: str,
        top_k: int,
        score_threshold: float
    ) -> List[Dict]:
        """Extract PPA details information using granular field searches."""

        logger.info("📋 Extracting PPA details with granular approach")

        # Get the vector store for ppa_details
        vector_store = self._get_field_vector_store("ppa_details")
        if not vector_store:
            logger.warning("No vector store for ppa_details")
            return []

        # Define granular searches for PPA components
        granular_searches = {
            "ppa_capacity": f"{plant_name} PPA capacity MW power purchase agreement contract",
            "ppa_counterparty": f"{plant_name} PPA counterparty buyer utility distribution company",
            "ppa_duration": f"{plant_name} PPA duration years contract period tenure",
            "ppa_pricing": f"{plant_name} PPA price tariff rate rupees MW pricing",
            "ppa_start_date": f"{plant_name} PPA start date commissioning commercial operation",
            "ppa_percentage": f"{plant_name} PPA percentage power sale distribution"
        }

        extracted_data = {}

        # Extract each component separately
        for component, query in granular_searches.items():
            logger.info(f"🔍 Searching for {component}: '{query}'")

            # Perform vector search for this component
            relevant_chunks = self._vector_similarity_search(
                vector_store, query, top_k, score_threshold
            )

            if relevant_chunks:
                logger.info(f"Found {len(relevant_chunks)} chunks for {component}")

                # Extract this component using LLM
                component_value = await self._extract_ppa_component(
                    component, relevant_chunks, plant_name
                )

                if component_value:
                    extracted_data[component] = component_value
                    logger.info(f"✅ Extracted {component}: {component_value}")
            else:
                logger.info(f"❌ No chunks found for {component}")

        # Combine extracted components into structured format
        return self._build_ppa_details_structure(extracted_data)

    def _construct_field_query(self, field_name: str, plant_name: str) -> str:
        """Construct field-specific search query."""

        base_query = f"{plant_name}"

        field_specific_terms = {
            "lat": "latitude coordinates GPS location decimal degrees",
            "long": "longitude coordinates GPS location decimal degrees",
            "grid_connectivity_maps": "substation transmission grid connectivity voltage kV switchyard HVPNL",
            "ppa_details": "power purchase agreement PPA contract pricing capacity MW distribution utility",
            "units_id": "units turbines generators capacity MW operational",
            "plant_address": "address location district state country",
            "name": "official name power plant facility"
        }

        if field_name in field_specific_terms:
            return f"{base_query} {field_specific_terms[field_name]}"
        else:
            return f"{base_query} {field_name}"

    def _vector_similarity_search(
        self,
        vector_store: Dict,
        query: str,
        top_k: int,
        score_threshold: float
    ) -> List[Dict]:
        """Perform vector similarity search."""

        # Load embedding model
        self._load_embedding_model()

        # Encode query
        query_embedding = self.embedding_model.encode([query])
        faiss.normalize_L2(query_embedding)

        # Search
        index = vector_store["index"]
        scores, indices = index.search(query_embedding.astype('float32'), top_k)

        # Filter by score threshold and prepare results
        relevant_chunks = []
        chunks = vector_store["chunks"]
        metadatas = vector_store["metadatas"]

        for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
            if score >= score_threshold and idx < len(chunks):
                relevant_chunks.append({
                    "chunk_text": chunks[idx],
                    "metadata": metadatas[idx],
                    "similarity_score": float(score),
                    "rank": i + 1
                })

        # Sort by similarity score (descending)
        relevant_chunks.sort(key=lambda x: x["similarity_score"], reverse=True)

        return relevant_chunks

    async def _extract_grid_component(
        self,
        component_name: str,
        relevant_chunks: List[Dict],
        plant_name: str
    ) -> Optional[str]:
        """Extract a specific grid connectivity component using LLM."""

        # Combine retrieved content
        combined_content = self._combine_retrieved_content(relevant_chunks)

        # Component-specific prompts
        component_prompts = {
            "substation_names": f"""
From the following content about {plant_name}, extract the names of substations connected to this power plant.
Look for substation names, switchyard names, or grid connection points.

Content: {combined_content[:4000]}

IMPORTANT: Return ONLY the substation names separated by commas, nothing else.
Example: "Sonipat Substation, Mahindergarh Substation"
If not found, return "".
""",
            "voltage_levels": f"""
From the following content about {plant_name}, extract the voltage levels of transmission lines.
Look for voltage specifications like "400 kV", "220 kV", etc.

Content: {combined_content[:4000]}

IMPORTANT: Return ONLY the voltage levels separated by commas, nothing else.
Example: "400 kV, 220 kV"
If not found, return "".
""",
            "transmission_lines": f"""
From the following content about {plant_name}, extract information about transmission lines.
Look for transmission line details, power evacuation lines, or grid connections.

Content: {combined_content[:4000]}

IMPORTANT: Return ONLY the transmission line information, nothing else.
If not found, return "".
""",
            "grid_operator": f"""
From the following content about {plant_name}, extract the grid operator or transmission utility name.
Look for utility names like "HVPNL", "PGCIL", or other transmission companies.

Content: {combined_content[:4000]}

IMPORTANT: Return ONLY the grid operator name, nothing else.
If not found, return "".
""",
            "connection_points": f"""
From the following content about {plant_name}, extract grid connection point details.
Look for where the plant connects to the grid, tie-in points, or connection locations.

Content: {combined_content[:4000]}

IMPORTANT: Return ONLY the connection point information, nothing else.
If not found, return "".
"""
        }

        if component_name not in component_prompts:
            logger.warning(f"Unknown grid component: {component_name}")
            return None

        prompt = component_prompts[component_name]

        try:
            raw_response = await self._call_llm_client(prompt)
            if raw_response and raw_response.strip() and raw_response.strip() not in ["", "unknown", "not found"]:
                return raw_response.strip()
        except Exception as e:
            logger.error(f"Error extracting grid component {component_name}: {e}")

        return None

    async def _extract_ppa_component(
        self,
        component_name: str,
        relevant_chunks: List[Dict],
        plant_name: str
    ) -> Optional[str]:
        """Extract a specific PPA component using LLM."""

        # Combine retrieved content
        combined_content = self._combine_retrieved_content(relevant_chunks)

        # Component-specific prompts
        component_prompts = {
            "ppa_capacity": f"""
From the following content about {plant_name}, extract the PPA capacity or contracted capacity.
Look for capacity figures in MW related to power purchase agreements.

Content: {combined_content[:4000]}

IMPORTANT: Return ONLY the capacity with unit, nothing else.
Example: "660 MW" or "1320 MW"
If not found, return "".
""",
            "ppa_counterparty": f"""
From the following content about {plant_name}, extract the PPA counterparty or power buyer.
Look for utility names, distribution companies, or government entities buying power.

Content: {combined_content[:4000]}

IMPORTANT: Return ONLY the counterparty name, nothing else.
Example: "Haryana State Electricity Board" or "HVPNL"
If not found, return "".
""",
            "ppa_duration": f"""
From the following content about {plant_name}, extract the PPA duration or contract period.
Look for contract duration, tenure, or agreement period in years.

Content: {combined_content[:4000]}

IMPORTANT: Return ONLY the duration with unit, nothing else.
Example: "25 years" or "20 years"
If not found, return "".
""",
            "ppa_pricing": f"""
From the following content about {plant_name}, extract PPA pricing or tariff information.
Look for price per unit, tariff rates, or pricing details.

Content: {combined_content[:4000]}

IMPORTANT: Return ONLY the pricing information, nothing else.
Example: "Rs 3.50/kWh" or "$0.05/kWh"
If not found, return "".
""",
            "ppa_start_date": f"""
From the following content about {plant_name}, extract the PPA start date or commercial operation date.
Look for commissioning dates, commercial operation dates, or PPA commencement.

Content: {combined_content[:4000]}

IMPORTANT: Return ONLY the date, nothing else.
Example: "2012-03-01" or "March 2012"
If not found, return "".
""",
            "ppa_percentage": f"""
From the following content about {plant_name}, extract the percentage of power sold under PPA.
Look for percentage of power allocation, distribution percentages.

Content: {combined_content[:4000]}

IMPORTANT: Return ONLY the percentage, nothing else.
Example: "90%" or "100%"
If not found, return "".
"""
        }

        if component_name not in component_prompts:
            logger.warning(f"Unknown PPA component: {component_name}")
            return None

        prompt = component_prompts[component_name]

        try:
            raw_response = await self._call_llm_client(prompt)
            if raw_response and raw_response.strip() and raw_response.strip() not in ["", "unknown", "not found"]:
                return raw_response.strip()
        except Exception as e:
            logger.error(f"Error extracting PPA component {component_name}: {e}")

        return None

    def _build_grid_connectivity_structure(self, extracted_data: Dict) -> List[Dict]:
        """Build structured grid connectivity data from extracted components."""

        if not extracted_data:
            return []

        # Build the structure based on extracted data
        grid_structure = {
            "description": "Grid connectivity information",
            "details": []
        }

        # Add substation details if available
        if "substation_names" in extracted_data:
            substations = extracted_data["substation_names"].split(",")
            for substation in substations:
                substation = substation.strip()
                if substation:
                    detail = {
                        "substation_name": substation,
                        "substation_type": extracted_data.get("voltage_levels", ""),
                        "capacity": "",
                        "latitude": "",
                        "longitude": "",
                        "projects": []
                    }
                    grid_structure["details"].append(detail)

        # If no substations found, create a general entry
        if not grid_structure["details"] and extracted_data:
            detail = {
                "substation_name": extracted_data.get("connection_points", ""),
                "substation_type": extracted_data.get("voltage_levels", ""),
                "capacity": "",
                "latitude": "",
                "longitude": "",
                "grid_operator": extracted_data.get("grid_operator", ""),
                "transmission_lines": extracted_data.get("transmission_lines", ""),
                "projects": []
            }
            grid_structure["details"].append(detail)

        return [grid_structure] if grid_structure["details"] else []

    def _build_ppa_details_structure(self, extracted_data: Dict) -> List[Dict]:
        """Build structured PPA details data from extracted components."""

        if not extracted_data:
            return []

        # Build the structure based on extracted data
        ppa_structure = {
            "description": "Power Purchase Agreement details",
            "capacity": extracted_data.get("ppa_capacity", ""),
            "capacity_unit": "MW" if "MW" in extracted_data.get("ppa_capacity", "") else "",
            "start_date": extracted_data.get("ppa_start_date", ""),
            "end_date": "",
            "tenure": None,
            "tenure_type": "",
            "respondents": []
        }

        # Extract duration information
        if "ppa_duration" in extracted_data:
            duration = extracted_data["ppa_duration"]
            if "year" in duration.lower():
                try:
                    years = int(''.join(filter(str.isdigit, duration)))
                    ppa_structure["tenure"] = years
                    ppa_structure["tenure_type"] = "Years"
                except:
                    pass

        # Add counterparty information
        if "ppa_counterparty" in extracted_data:
            respondent = {
                "name": extracted_data["ppa_counterparty"],
                "capacity": extracted_data.get("ppa_capacity", ""),
                "currency": "INR" if "Rs" in extracted_data.get("ppa_pricing", "") else "USD",
                "price": extracted_data.get("ppa_pricing", ""),
                "price_unit": "per kWh" if "kWh" in extracted_data.get("ppa_pricing", "") else "",
                "percentage": extracted_data.get("ppa_percentage", "")
            }
            ppa_structure["respondents"].append(respondent)

        return [ppa_structure] if any(extracted_data.values()) else []

    async def _extract_with_llm(
        self,
        field_name: str,
        relevant_chunks: List[Dict],
        plant_name: str
    ) -> Optional[Any]:
        """Extract field value using LLM with retrieved content."""

        if not self.llm_client:
            logger.error("No LLM client provided for extraction")
            return None

        # Combine retrieved content
        combined_content = self._combine_retrieved_content(relevant_chunks)

        # Get field-specific prompt
        prompt = self._get_extraction_prompt(field_name, plant_name, combined_content)

        try:
            # Detect and use appropriate LLM client
            raw_response = await self._call_llm_client(prompt)

            if not raw_response:
                logger.warning(f"Empty response from LLM for field {field_name}")
                return None

            # Process the response based on field type
            return self._process_llm_response(field_name, raw_response)

        except Exception as e:
            logger.error(f"Error in LLM extraction for {field_name}: {e}")
            return None

    async def _call_llm_client(self, prompt: str) -> Optional[str]:
        """Call the appropriate LLM client based on its type."""

        # Check for GroqExtractionClient (your existing Groq client)
        if hasattr(self.llm_client, 'client') and hasattr(self.llm_client.client, 'chat'):
            logger.debug("Using GroqExtractionClient for extraction")
            try:
                # Use the same pattern as your existing pipeline
                response = self.llm_client.client.chat.completions.create(
                    model=getattr(self.llm_client, 'model', 'llama-3.3-70b-versatile'),
                    messages=[
                        {
                            "role": "system",
                            "content": "You are an expert at extracting technical information about power plants from documents. Be precise and only extract information that is explicitly stated."
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    temperature=0.1,
                    max_tokens=1000
                )
                return response.choices[0].message.content.strip()
            except Exception as e:
                logger.error(f"GroqExtractionClient call failed: {e}")
                return None

        # Check for OpenAIExtractionClient (your existing OpenAI client)
        elif hasattr(self.llm_client, 'extract_field') and hasattr(self.llm_client, '_extract_with_prompt'):
            logger.debug("Using OpenAIExtractionClient for extraction")
            try:
                # Use the direct prompt method from your OpenAI client
                return await self.llm_client._extract_with_prompt(prompt)
            except Exception as e:
                logger.error(f"OpenAIExtractionClient call failed: {e}")
                return None

        # Check for direct OpenAI client (has chat.completions)
        elif hasattr(self.llm_client, 'chat') and hasattr(self.llm_client.chat, 'completions'):
            logger.debug("Using direct OpenAI client for extraction")
            try:
                import asyncio
                response = await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: self.llm_client.chat.completions.create(
                        model="gpt-4o-mini",
                        messages=[
                            {
                                "role": "system",
                                "content": "You are an expert at extracting technical information about power plants from documents. Be precise and only extract information that is explicitly stated."
                            },
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ],
                        temperature=0.1,
                        max_tokens=1000
                    )
                )
                return response.choices[0].message.content.strip()
            except Exception as e:
                logger.error(f"Direct OpenAI client call failed: {e}")
                return None

        # Check for mock clients (for testing)
        elif hasattr(self.llm_client, 'extract_field') and not hasattr(self.llm_client, 'client'):
            logger.debug("Using mock client for extraction")
            try:
                return await self.llm_client.extract_field(prompt)
            except Exception as e:
                logger.error(f"Mock client call failed: {e}")
                return None

        # Check for mock OpenAI client (for testing)
        elif hasattr(self.llm_client, 'chat') and hasattr(self.llm_client.chat, 'create'):
            logger.debug("Using Mock OpenAI client for extraction")
            try:
                response = await self.llm_client.chat.create(
                    model="gpt-4o-mini",
                    messages=[
                        {
                            "role": "system",
                            "content": "You are an expert at extracting technical information about power plants from documents. Be precise and only extract information that is explicitly stated."
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    temperature=0.1,
                    max_tokens=1000
                )
                return response.choices[0].message.content.strip()
            except Exception as e:
                logger.error(f"Mock OpenAI client call failed: {e}")
                return None

        else:
            logger.error(f"Unsupported LLM client type: {type(self.llm_client)}")
            logger.error(f"Client attributes: {dir(self.llm_client)}")
            return None

    def _combine_retrieved_content(self, relevant_chunks: List[Dict]) -> str:
        """Combine retrieved chunks into a single content string."""

        combined_parts = []
        for i, chunk_data in enumerate(relevant_chunks):
            chunk_text = chunk_data["chunk_text"]
            similarity_score = chunk_data["similarity_score"]
            source_url = chunk_data["metadata"].get("source_url", "unknown")

            combined_parts.append(
                f"--- Relevant Content {i+1} (similarity: {similarity_score:.3f}) ---\n"
                f"Source: {source_url}\n"
                f"{chunk_text}\n"
            )

        return "\n".join(combined_parts)

    def _get_extraction_prompt(self, field_name: str, plant_name: str, content: str) -> str:
        """Get field-specific extraction prompt."""

        # Import prompts from config
        try:
            from src.config import config
            prompts = config.plant_details_extraction_prompts

            if field_name in prompts:
                prompt_template = prompts[field_name]
                logger.debug(f"Using config prompt for field: {field_name}")
                return prompt_template.format(
                    plant_name=plant_name,
                    content=content[:8000]  # Limit content length
                )
        except Exception as e:
            logger.warning(f"Could not load prompts from config for field {field_name}: {e}")

        # Enhanced fallback prompts for specific fields
        enhanced_prompts = {
            "lat": f"""
From the following content about {plant_name}, extract the latitude coordinate in decimal degrees.
Look for GPS coordinates, geographic location data, or map coordinates.

Content: {content[:8000]}

IMPORTANT: Return ONLY the latitude number in decimal degrees, nothing else.
Valid range: -90 to +90. If not found, return "".
""",
            "long": f"""
From the following content about {plant_name}, extract the longitude coordinate in decimal degrees.
Look for GPS coordinates, geographic location data, or map coordinates.

Content: {content[:8000]}

IMPORTANT: Return ONLY the longitude number in decimal degrees, nothing else.
Valid range: -180 to +180. If not found, return "".
""",
            "plant_address": f"""
From the following content about {plant_name}, extract the location address.
Look for district, city, state, and country information. Also look for village names, location descriptions.

Content: {content[:8000]}

IMPORTANT: Return ONLY the address in format "Village/District, State, Country", nothing else.
Example: "Jharli Village, Jhajjar District, Haryana, India"
If not found, return "".
""",
            "plant_type": f"""
From the following content about {plant_name}, determine the power generation technology.
Look for fuel type or generation method: coal, gas, nuclear, solar, wind, hydro, etc.

Content: {content[:8000]}

IMPORTANT: Return ONLY the plant type, nothing else.
Valid responses: coal, gas, nuclear, solar, wind, hydro, biomass, geothermal, oil
If not found, return "".
""",
            "units_id": f"""
From the following content about {plant_name}, find operational units and their numbers.
Look for unit numbers, generating units, or operational capacity information.

Content: {content[:8000]}

IMPORTANT: Return ONLY a JSON array of integers representing unit numbers.
Example: [1, 2] or [1, 2, 3, 4]
If not found, return: []
""",
            "grid_connectivity_maps": f"""
From the following content about {plant_name}, extract grid connection information.
Look for substation names, transmission lines, voltage levels (kV), grid connections.

Content: {content[:8000]}

IMPORTANT: Return ONLY valid JSON array with grid connection details.
Example: [{{"substation_name": "Name", "voltage": "400kV", "distance": "50km"}}]
If not found, return: []
""",
            "ppa_details": f"""
From the following content about {plant_name}, extract Power Purchase Agreement details.
Look for contract capacity, duration, counterparty, pricing information.

Content: {content[:8000]}

IMPORTANT: Return ONLY valid JSON array with PPA details.
Example: [{{"capacity": "660MW", "counterparty": "State Utility", "duration": "25 years"}}]
If not found, return: []
"""
        }

        if field_name in enhanced_prompts:
            logger.debug(f"Using enhanced fallback prompt for field: {field_name}")
            return enhanced_prompts[field_name]

        # Generic fallback prompt
        logger.debug(f"Using generic fallback prompt for field: {field_name}")
        return f"""
From the following content about {plant_name}, extract information for the field: {field_name}.

Content: {content[:8000]}

Please extract the relevant information and return it in the appropriate format.
If no relevant information is found, return an empty value.
"""

    def _process_llm_response(self, field_name: str, raw_response: str) -> Any:
        """Process LLM response based on field type."""

        if not raw_response or raw_response.lower() in ["unknown", "not found", "n/a", "", "none"]:
            return self._get_default_value(field_name)

        # Handle JSON fields
        if field_name in ["units_id", "grid_connectivity_maps", "ppa_details"]:
            try:
                # Try to extract JSON from response
                import re
                json_match = re.search(r'\[.*\]', raw_response, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    parsed_value = json.loads(json_str)
                    return parsed_value
                else:
                    return self._get_default_value(field_name)
            except json.JSONDecodeError:
                logger.warning(f"Could not parse JSON response for {field_name}")
                return self._get_default_value(field_name)

        # Handle simple string fields
        return raw_response.strip()

    def _get_default_value(self, field_name: str) -> Any:
        """Get default value for field type."""
        if field_name in ["units_id", "grid_connectivity_maps", "ppa_details"]:
            return []
        else:
            return ""

    def get_available_fields(self) -> List[str]:
        """Get list of fields available for RAG extraction."""
        if not self.metadata:
            return []
        return list(self.metadata.get("fields", {}).keys())

    def get_field_stats(self, field_name: str) -> Optional[Dict]:
        """Get statistics for a specific field's vector store."""
        if not self.metadata or field_name not in self.metadata.get("fields", {}):
            return None

        return self.metadata["fields"][field_name]
