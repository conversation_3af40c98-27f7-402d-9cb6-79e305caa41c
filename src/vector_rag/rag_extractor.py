"""
RAG-based field extraction using vector similarity search.

This module provides extraction capabilities using vector databases
created from targeted content, with fallback to traditional methods.
"""

import json
import logging
from typing import Dict, List, Optional, Any, Tuple
import asyncio

try:
    import faiss
    import numpy as np
    from sentence_transformers import SentenceTransformer
    VECTOR_DEPS_AVAILABLE = True
except ImportError:
    VECTOR_DEPS_AVAILABLE = False
    faiss = None
    np = None
    SentenceTransformer = None

from .vectorizer import TargetedContentVectorizer

logger = logging.getLogger(__name__)


class VectorRAGExtractor:
    """Extract fields using vector similarity search on targeted content."""

    def __init__(
        self,
        vector_db_path: str,
        embedding_model: str = "all-MiniLM-L6-v2",
        llm_client = None
    ):
        """
        Initialize the RAG extractor.

        Args:
            vector_db_path: Path to the vector database directory
            embedding_model: Name of the sentence transformer model
            llm_client: LLM client for extraction (OpenAI or Groq)
        """
        if not VECTOR_DEPS_AVAILABLE:
            raise ImportError(
                "Vector dependencies not available. Please install: "
                "pip install faiss-cpu sentence-transformers numpy"
            )

        self.vector_db_path = vector_db_path
        self.embedding_model_name = embedding_model
        self.embedding_model = None  # Lazy load
        self.llm_client = llm_client

        # Lazy-loaded field vector stores
        self.field_stores = {}

        # Load metadata
        self.metadata = self._load_metadata()

    def _load_metadata(self) -> Optional[Dict]:
        """Load vector database metadata."""
        try:
            metadata_file = f"{self.vector_db_path}/metadata.json"
            with open(metadata_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading metadata: {e}")
            return None

    def _load_embedding_model(self):
        """Lazy load the embedding model."""
        if self.embedding_model is None:
            logger.info(f"Loading embedding model: {self.embedding_model_name}")
            self.embedding_model = SentenceTransformer(self.embedding_model_name)

    def _get_field_vector_store(self, field_name: str) -> Optional[Dict]:
        """Load vector store for a specific field (with caching)."""
        if field_name in self.field_stores:
            return self.field_stores[field_name]

        if not self.metadata or field_name not in self.metadata.get("fields", {}):
            logger.warning(f"No vector store available for field: {field_name}")
            return None

        # Load the vector store
        vectorizer = TargetedContentVectorizer()
        vector_store = vectorizer.load_field_vector_store(self.vector_db_path, field_name)

        if vector_store:
            self.field_stores[field_name] = vector_store
            logger.info(f"Loaded vector store for field: {field_name}")

        return vector_store

    async def extract_field_with_rag(
        self,
        field_name: str,
        plant_name: str,
        top_k: int = 3,
        score_threshold: float = 0.7
    ) -> Optional[Any]:
        """
        Extract field using vector similarity search + LLM.

        Args:
            field_name: The field to extract (e.g., "grid_connectivity_maps")
            plant_name: Plant name for context
            top_k: Number of top similar chunks to retrieve
            score_threshold: Minimum similarity score threshold

        Returns:
            Extracted field value or None if extraction fails
        """
        logger.info(f"Starting RAG extraction for field: {field_name}")

        # Get vector store for this field
        vector_store = self._get_field_vector_store(field_name)
        if not vector_store:
            logger.warning(f"No vector store for field {field_name}, cannot use RAG")
            return None

        # Construct field-specific query
        query = self._construct_field_query(field_name, plant_name)
        logger.info(f"RAG query: '{query}'")

        # Perform vector similarity search
        relevant_chunks = self._vector_similarity_search(
            vector_store, query, top_k, score_threshold
        )

        if not relevant_chunks:
            logger.warning(f"No relevant chunks found for field {field_name}")
            return None

        logger.info(f"Found {len(relevant_chunks)} relevant chunks for {field_name}")

        # Extract using LLM with retrieved content
        return await self._extract_with_llm(field_name, relevant_chunks, plant_name)

    def _construct_field_query(self, field_name: str, plant_name: str) -> str:
        """Construct field-specific search query."""

        base_query = f"{plant_name}"

        field_specific_terms = {
            "lat": "latitude coordinates GPS location decimal degrees",
            "long": "longitude coordinates GPS location decimal degrees",
            "grid_connectivity_maps": "substation transmission grid connectivity voltage kV switchyard HVPNL",
            "ppa_details": "power purchase agreement PPA contract pricing capacity MW distribution utility",
            "units_id": "units turbines generators capacity MW operational",
            "plant_address": "address location district state country",
            "name": "official name power plant facility"
        }

        if field_name in field_specific_terms:
            return f"{base_query} {field_specific_terms[field_name]}"
        else:
            return f"{base_query} {field_name}"

    def _vector_similarity_search(
        self,
        vector_store: Dict,
        query: str,
        top_k: int,
        score_threshold: float
    ) -> List[Dict]:
        """Perform vector similarity search."""

        # Load embedding model
        self._load_embedding_model()

        # Encode query
        query_embedding = self.embedding_model.encode([query])
        faiss.normalize_L2(query_embedding)

        # Search
        index = vector_store["index"]
        scores, indices = index.search(query_embedding.astype('float32'), top_k)

        # Filter by score threshold and prepare results
        relevant_chunks = []
        chunks = vector_store["chunks"]
        metadatas = vector_store["metadatas"]

        for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
            if score >= score_threshold and idx < len(chunks):
                relevant_chunks.append({
                    "chunk_text": chunks[idx],
                    "metadata": metadatas[idx],
                    "similarity_score": float(score),
                    "rank": i + 1
                })

        # Sort by similarity score (descending)
        relevant_chunks.sort(key=lambda x: x["similarity_score"], reverse=True)

        return relevant_chunks

    async def _extract_with_llm(
        self,
        field_name: str,
        relevant_chunks: List[Dict],
        plant_name: str
    ) -> Optional[Any]:
        """Extract field value using LLM with retrieved content."""

        if not self.llm_client:
            logger.error("No LLM client provided for extraction")
            return None

        # Combine retrieved content
        combined_content = self._combine_retrieved_content(relevant_chunks)

        # Get field-specific prompt
        prompt = self._get_extraction_prompt(field_name, plant_name, combined_content)

        try:
            # Detect and use appropriate LLM client
            raw_response = await self._call_llm_client(prompt)

            if not raw_response:
                logger.warning(f"Empty response from LLM for field {field_name}")
                return None

            # Process the response based on field type
            return self._process_llm_response(field_name, raw_response)

        except Exception as e:
            logger.error(f"Error in LLM extraction for {field_name}: {e}")
            return None

    async def _call_llm_client(self, prompt: str) -> Optional[str]:
        """Call the appropriate LLM client based on its type."""

        # Check for GroqExtractionClient (your existing Groq client)
        if hasattr(self.llm_client, 'client') and hasattr(self.llm_client.client, 'chat'):
            logger.debug("Using GroqExtractionClient for extraction")
            try:
                # Use the same pattern as your existing pipeline
                response = self.llm_client.client.chat.completions.create(
                    model=getattr(self.llm_client, 'model', 'llama-3.3-70b-versatile'),
                    messages=[
                        {
                            "role": "system",
                            "content": "You are an expert at extracting technical information about power plants from documents. Be precise and only extract information that is explicitly stated."
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    temperature=0.1,
                    max_tokens=1000
                )
                return response.choices[0].message.content.strip()
            except Exception as e:
                logger.error(f"GroqExtractionClient call failed: {e}")
                return None

        # Check for OpenAIExtractionClient (your existing OpenAI client)
        elif hasattr(self.llm_client, 'extract_field') and hasattr(self.llm_client, '_extract_with_prompt'):
            logger.debug("Using OpenAIExtractionClient for extraction")
            try:
                # Use the direct prompt method from your OpenAI client
                return await self.llm_client._extract_with_prompt(prompt)
            except Exception as e:
                logger.error(f"OpenAIExtractionClient call failed: {e}")
                return None

        # Check for direct OpenAI client (has chat.completions)
        elif hasattr(self.llm_client, 'chat') and hasattr(self.llm_client.chat, 'completions'):
            logger.debug("Using direct OpenAI client for extraction")
            try:
                import asyncio
                response = await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: self.llm_client.chat.completions.create(
                        model="gpt-4o-mini",
                        messages=[
                            {
                                "role": "system",
                                "content": "You are an expert at extracting technical information about power plants from documents. Be precise and only extract information that is explicitly stated."
                            },
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ],
                        temperature=0.1,
                        max_tokens=1000
                    )
                )
                return response.choices[0].message.content.strip()
            except Exception as e:
                logger.error(f"Direct OpenAI client call failed: {e}")
                return None

        # Check for mock clients (for testing)
        elif hasattr(self.llm_client, 'extract_field') and not hasattr(self.llm_client, 'client'):
            logger.debug("Using mock client for extraction")
            try:
                return await self.llm_client.extract_field(prompt)
            except Exception as e:
                logger.error(f"Mock client call failed: {e}")
                return None

        # Check for mock OpenAI client (for testing)
        elif hasattr(self.llm_client, 'chat') and hasattr(self.llm_client.chat, 'create'):
            logger.debug("Using Mock OpenAI client for extraction")
            try:
                response = await self.llm_client.chat.create(
                    model="gpt-4o-mini",
                    messages=[
                        {
                            "role": "system",
                            "content": "You are an expert at extracting technical information about power plants from documents. Be precise and only extract information that is explicitly stated."
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    temperature=0.1,
                    max_tokens=1000
                )
                return response.choices[0].message.content.strip()
            except Exception as e:
                logger.error(f"Mock OpenAI client call failed: {e}")
                return None

        else:
            logger.error(f"Unsupported LLM client type: {type(self.llm_client)}")
            logger.error(f"Client attributes: {dir(self.llm_client)}")
            return None

    def _combine_retrieved_content(self, relevant_chunks: List[Dict]) -> str:
        """Combine retrieved chunks into a single content string."""

        combined_parts = []
        for i, chunk_data in enumerate(relevant_chunks):
            chunk_text = chunk_data["chunk_text"]
            similarity_score = chunk_data["similarity_score"]
            source_url = chunk_data["metadata"].get("source_url", "unknown")

            combined_parts.append(
                f"--- Relevant Content {i+1} (similarity: {similarity_score:.3f}) ---\n"
                f"Source: {source_url}\n"
                f"{chunk_text}\n"
            )

        return "\n".join(combined_parts)

    def _get_extraction_prompt(self, field_name: str, plant_name: str, content: str) -> str:
        """Get field-specific extraction prompt."""

        # Import prompts from config
        try:
            from src.config import config
            prompts = config.plant_details_extraction_prompts

            if field_name in prompts:
                return prompts[field_name].format(
                    plant_name=plant_name,
                    content=content[:8000]  # Limit content length
                )
        except Exception as e:
            logger.warning(f"Could not load prompts from config: {e}")

        # Fallback generic prompt
        return f"""
From the following content about {plant_name}, extract information for the field: {field_name}.

Content: {content[:8000]}

Please extract the relevant information and return it in the appropriate format.
If no relevant information is found, return an empty value.
"""

    def _process_llm_response(self, field_name: str, raw_response: str) -> Any:
        """Process LLM response based on field type."""

        if not raw_response or raw_response.lower() in ["unknown", "not found", "n/a", "", "none"]:
            return self._get_default_value(field_name)

        # Handle JSON fields
        if field_name in ["units_id", "grid_connectivity_maps", "ppa_details"]:
            try:
                # Try to extract JSON from response
                import re
                json_match = re.search(r'\[.*\]', raw_response, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    parsed_value = json.loads(json_str)
                    return parsed_value
                else:
                    return self._get_default_value(field_name)
            except json.JSONDecodeError:
                logger.warning(f"Could not parse JSON response for {field_name}")
                return self._get_default_value(field_name)

        # Handle simple string fields
        return raw_response.strip()

    def _get_default_value(self, field_name: str) -> Any:
        """Get default value for field type."""
        if field_name in ["units_id", "grid_connectivity_maps", "ppa_details"]:
            return []
        else:
            return ""

    def get_available_fields(self) -> List[str]:
        """Get list of fields available for RAG extraction."""
        if not self.metadata:
            return []
        return list(self.metadata.get("fields", {}).keys())

    def get_field_stats(self, field_name: str) -> Optional[Dict]:
        """Get statistics for a specific field's vector store."""
        if not self.metadata or field_name not in self.metadata.get("fields", {}):
            return None

        return self.metadata["fields"][field_name]
