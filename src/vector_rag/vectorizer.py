"""
Vector database creation from targeted_content JSON files.

This module handles the creation of field-specific FAISS vector databases
from the targeted_content JSON files generated by the scraping pipeline.
"""

import json
import os
import logging
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import asyncio

try:
    import faiss
    import numpy as np
    from sentence_transformers import SentenceTransformer
    VECTOR_DEPS_AVAILABLE = True
except ImportError:
    VECTOR_DEPS_AVAILABLE = False
    faiss = None
    np = None
    SentenceTransformer = None

from .field_chunkers import FieldSpecificChunker

logger = logging.getLogger(__name__)


class TargetedContentVectorizer:
    """Build field-specific FAISS vector databases from targeted_content JSON files."""
    
    def __init__(self, embedding_model: str = "all-MiniLM-L6-v2"):
        """
        Initialize the vectorizer.
        
        Args:
            embedding_model: Name of the sentence transformer model to use
        """
        if not VECTOR_DEPS_AVAILABLE:
            raise ImportError(
                "Vector dependencies not available. Please install: "
                "pip install faiss-cpu sentence-transformers numpy"
            )
        
        self.embedding_model_name = embedding_model
        self.embedding_model = None  # Lazy load
        self.chunker = FieldSpecificChunker()
        self.vector_stores = {}  # field_name -> vector store info
        
    def _load_embedding_model(self):
        """Lazy load the embedding model."""
        if self.embedding_model is None:
            logger.info(f"Loading embedding model: {self.embedding_model_name}")
            self.embedding_model = SentenceTransformer(self.embedding_model_name)
            logger.info("Embedding model loaded successfully")
    
    async def build_vector_db_from_targeted_content(
        self, 
        targeted_content_file: str,
        output_dir: str = "vector_db"
    ) -> Dict[str, Any]:
        """
        Build field-specific vector databases from targeted_content JSON.
        
        Args:
            targeted_content_file: Path to the targeted_content JSON file
            output_dir: Directory to save vector databases
            
        Returns:
            Dictionary with vector store information for each field
        """
        logger.info(f"Building vector databases from: {targeted_content_file}")
        
        # Load the targeted content
        with open(targeted_content_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        plant_name = data["plant_name"]
        timestamp = data["timestamp"]
        
        # Create output directory structure
        plant_vector_dir = os.path.join(output_dir, f"{plant_name.lower().replace(' ', '_')}_{timestamp}")
        os.makedirs(plant_vector_dir, exist_ok=True)
        
        # Process each field
        vector_store_info = {
            "plant_name": plant_name,
            "timestamp": timestamp,
            "vector_db_path": plant_vector_dir,
            "fields": {}
        }
        
        for field_name, content_list in data["targeted_searches"].items():
            if not content_list:
                logger.info(f"No content for field '{field_name}', skipping")
                continue
                
            logger.info(f"Processing field: {field_name} ({len(content_list)} URLs)")
            
            try:
                field_info = await self._build_field_vector_store(
                    field_name, content_list, plant_name, plant_vector_dir
                )
                
                if field_info:
                    vector_store_info["fields"][field_name] = field_info
                    logger.info(f"✅ Created vector store for {field_name}: {field_info['chunk_count']} chunks")
                else:
                    logger.warning(f"Failed to create vector store for {field_name}")
                    
            except Exception as e:
                logger.error(f"Error processing field {field_name}: {e}")
                continue
        
        # Save metadata
        metadata_file = os.path.join(plant_vector_dir, "metadata.json")
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(vector_store_info, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Vector databases created successfully in: {plant_vector_dir}")
        return vector_store_info
    
    async def _build_field_vector_store(
        self, 
        field_name: str, 
        content_list: List[Dict], 
        plant_name: str,
        output_dir: str
    ) -> Optional[Dict[str, Any]]:
        """Build FAISS index for a specific field."""
        
        # Process all URLs for this field
        all_chunks = []
        all_metadatas = []
        
        for url_data in content_list:
            chunks, metadatas = self._process_url_content_for_field(
                field_name, url_data, plant_name
            )
            all_chunks.extend(chunks)
            all_metadatas.extend(metadatas)
        
        if not all_chunks:
            logger.warning(f"No chunks generated for field {field_name}")
            return None
        
        logger.info(f"Generated {len(all_chunks)} chunks for field {field_name}")
        
        # Generate embeddings
        self._load_embedding_model()
        
        logger.info(f"Generating embeddings for {len(all_chunks)} chunks...")
        embeddings = self.embedding_model.encode(all_chunks, show_progress_bar=True)
        
        # Create FAISS index
        dimension = embeddings.shape[1]
        index = faiss.IndexFlatIP(dimension)  # Inner product for cosine similarity
        
        # Normalize embeddings for cosine similarity
        faiss.normalize_L2(embeddings)
        index.add(embeddings.astype('float32'))
        
        # Save FAISS index
        index_file = os.path.join(output_dir, f"{field_name}_vectors.faiss")
        faiss.write_index(index, index_file)
        
        # Save chunks and metadata
        chunks_file = os.path.join(output_dir, f"{field_name}_chunks.json")
        with open(chunks_file, 'w', encoding='utf-8') as f:
            json.dump({
                "chunks": all_chunks,
                "metadatas": all_metadatas
            }, f, indent=2, ensure_ascii=False)
        
        return {
            "field_name": field_name,
            "chunk_count": len(all_chunks),
            "embedding_dimension": dimension,
            "index_file": index_file,
            "chunks_file": chunks_file,
            "created_at": datetime.now().isoformat()
        }
    
    def _process_url_content_for_field(
        self, 
        field_name: str, 
        url_data: Dict, 
        plant_name: str
    ) -> Tuple[List[str], List[Dict]]:
        """Process content from a single URL based on field type."""
        
        content = url_data["content"]
        url = url_data["url"]
        relevance_score = url_data["relevance_score"]
        source_type = url_data["source_type"]
        content_length = url_data["content_length"]
        
        # Use field-specific chunking
        chunks = self.chunker.chunk_content_for_field(field_name, content)
        
        # Create metadata for each chunk
        metadatas = []
        for i, chunk in enumerate(chunks):
            metadatas.append({
                "field_name": field_name,
                "plant_name": plant_name,
                "source_url": url,
                "source_type": source_type,
                "relevance_score": relevance_score,
                "original_content_length": content_length,
                "chunk_index": i,
                "chunk_length": len(chunk),
                "chunk_text": chunk[:200] + "..." if len(chunk) > 200 else chunk  # Preview
            })
        
        return chunks, metadatas
    
    def load_field_vector_store(self, vector_db_path: str, field_name: str) -> Optional[Dict]:
        """
        Load a specific field's vector store from disk.
        
        Args:
            vector_db_path: Path to the vector database directory
            field_name: Name of the field to load
            
        Returns:
            Dictionary containing the loaded vector store components
        """
        if not VECTOR_DEPS_AVAILABLE:
            raise ImportError("Vector dependencies not available")
        
        index_file = os.path.join(vector_db_path, f"{field_name}_vectors.faiss")
        chunks_file = os.path.join(vector_db_path, f"{field_name}_chunks.json")
        
        if not os.path.exists(index_file) or not os.path.exists(chunks_file):
            logger.error(f"Vector store files not found for field {field_name}")
            return None
        
        try:
            # Load FAISS index
            index = faiss.read_index(index_file)
            
            # Load chunks and metadata
            with open(chunks_file, 'r', encoding='utf-8') as f:
                chunks_data = json.load(f)
            
            return {
                "field_name": field_name,
                "index": index,
                "chunks": chunks_data["chunks"],
                "metadatas": chunks_data["metadatas"]
            }
            
        except Exception as e:
            logger.error(f"Error loading vector store for field {field_name}: {e}")
            return None
