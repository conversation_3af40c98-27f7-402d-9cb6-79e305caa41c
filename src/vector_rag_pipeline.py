"""
Vector RAG Enhanced Power Plant Pipeline.

This pipeline extends the SimplePowerPlantPipeline with Vector RAG capabilities
for improved field extraction using semantic search on targeted content.
"""

import asyncio
import logging
import time
import os
from typing import Dict, List, Optional, Tuple
from datetime import datetime

from src.models import OrganizationalDetails, PlantDetails, ScrapedContent
from src.simple_pipeline import SimplePowerPlantPipeline
from src.vector_rag import VectorRAGEnhancedPipeline
from src.config import config

logger = logging.getLogger(__name__)


class VectorRAGPowerPlantPipeline(SimplePowerPlantPipeline, VectorRAGEnhancedPipeline):
    """
    Enhanced power plant pipeline with Vector RAG capabilities.

    This pipeline follows the same flow as SimplePowerPlantPipeline but adds
    Vector RAG extraction after targeted_content generation.
    """

    def __init__(self, enable_vector_rag: bool = True, use_openai: bool = False):
        """
        Initialize the Vector RAG enhanced pipeline.

        Args:
            enable_vector_rag: Whether to enable Vector RAG functionality
            use_openai: Whether to use OpenAI instead of Groq (fallback for rate limits)
        """
        # Initialize SimplePowerPlantPipeline
        SimplePowerPlantPipeline.__init__(self)

        # Override LLM clients if OpenAI is requested or Groq is rate limited
        if use_openai:
            self._setup_openai_clients()

        # Initialize Vector RAG capabilities
        VectorRAGEnhancedPipeline.__init__(
            self,
            llm_client=self.plant_extractor.llm_client,  # Use the same LLM client
            enable_vector_rag=enable_vector_rag
        )

        logger.info(f"Vector RAG Enhanced Pipeline initialized (RAG enabled: {enable_vector_rag}, OpenAI: {use_openai})")

    def _setup_openai_clients(self):
        """Setup OpenAI clients as fallback for rate limits."""
        try:
            from src.openai_client import OpenAIExtractionClient
            from src.enhanced_extractor import AdaptiveExtractor
            from src.plant_details_extractor import PlantDetailsExtractor

            # Check if OpenAI API key is available
            openai_api_key = getattr(config.pipeline, 'openai_api_key', None)
            if not openai_api_key:
                logger.warning("OpenAI API key not found, falling back to Groq")
                return

            logger.info(f"🔧 Setting up OpenAI clients with API key: {openai_api_key[:10]}...")

            # Replace extractors with OpenAI versions
            self.enhanced_extractor = AdaptiveExtractor(
                groq_api_key=config.pipeline.groq_api_key,  # Keep groq as fallback
                use_openai=True,
                openai_api_key=openai_api_key
            )

            # Create OpenAI plant extractor
            openai_client = OpenAIExtractionClient(openai_api_key)
            self.plant_extractor = PlantDetailsExtractor(
                groq_client=None,
                use_openai=True,
                openai_api_key=openai_api_key
            )

            logger.info("✅ OpenAI clients configured successfully")

        except Exception as e:
            logger.warning(f"Failed to setup OpenAI clients: {e}, using Groq")

    async def extract_plant_data(
        self,
        plant_name: str,
        use_existing_content: bool = True
    ) -> Tuple[Optional[OrganizationalDetails], Optional[PlantDetails], Dict]:
        """
        Extract plant data using enhanced pipeline with Vector RAG.

        Args:
            plant_name: Name of the power plant
            use_existing_content: Whether to use existing targeted_content files first

        Returns:
            Tuple of (organizational_details, plant_details, extraction_info)
        """
        start_time = time.time()
        extraction_info = {
            "strategy": "vector_rag_enhanced",
            "initial_search_time": 0,
            "missing_field_searches": 0,
            "cache_hit_fields": [],
            "missing_field_searches_list": [],
            "total_pages_scraped": 0,
            "vector_rag_enabled": self.enable_vector_rag,
            "vector_rag_extractions": {}
        }

        logger.info(f"🚀 Starting Vector RAG enhanced extraction for: {plant_name}")

        try:
            # Check if we should use existing targeted content first
            if use_existing_content and self.enable_vector_rag:
                targeted_content_file = self._find_targeted_content_file(plant_name)

                if targeted_content_file:
                    logger.info(f"📁 Found existing targeted content: {targeted_content_file}")
                    logger.info("🧠 Using Vector RAG-only mode with existing content")

                    # Use Vector RAG-only extraction
                    org_details, plant_details, extraction_info = await self._extract_with_existing_content(
                        plant_name, targeted_content_file, extraction_info
                    )

                    total_duration = time.time() - start_time
                    extraction_info["total_duration"] = total_duration
                    extraction_info["strategy"] = "vector_rag_only"

                    logger.info(f"✅ Vector RAG-only extraction completed in {total_duration:.2f}s")
                    return org_details, plant_details, extraction_info

            # Fallback to full pipeline if no existing content or disabled
            logger.info("📊 Running full pipeline (no existing content or Vector RAG disabled)")

            # Phase 1: Run the standard pipeline flow
            org_details, plant_details, base_extraction_info = await super().extract_plant_data(plant_name)

            # Merge base extraction info
            extraction_info.update(base_extraction_info)
            extraction_info["strategy"] = "vector_rag_enhanced"
            extraction_info["vector_rag_enabled"] = self.enable_vector_rag

            # Phase 2: Enhance with Vector RAG if enabled and targeted_content was created
            if self.enable_vector_rag and plant_details:
                # Look for the targeted_content file that was just created in this run
                fresh_targeted_content_file = self._find_targeted_content_file(plant_name)

                if fresh_targeted_content_file:
                    logger.info(f"📁 Found fresh targeted_content file: {fresh_targeted_content_file}")
                    plant_details, rag_info = await self._enhance_with_vector_rag_from_fresh_content(
                        plant_details, plant_name, fresh_targeted_content_file, extraction_info
                    )
                    extraction_info["vector_rag_extractions"] = rag_info
                else:
                    logger.warning("No targeted_content file found for Vector RAG enhancement")
                    extraction_info["vector_rag_extractions"] = {
                        "attempted_fields": [],
                        "successful_extractions": [],
                        "failed_extractions": [],
                        "vector_db_created": False,
                        "error": "No targeted_content file found"
                    }

            total_duration = time.time() - start_time
            extraction_info["total_duration"] = total_duration

            logger.info(f"✅ Vector RAG enhanced extraction completed in {total_duration:.2f}s")

            return org_details, plant_details, extraction_info

        except Exception as e:
            logger.error(f"Vector RAG enhanced extraction failed: {e}")
            raise

    async def _extract_with_existing_content(
        self,
        plant_name: str,
        targeted_content_file: str,
        extraction_info: Dict
    ) -> Tuple[Optional[OrganizationalDetails], Optional[PlantDetails], Dict]:
        """
        Extract plant data using only existing targeted content and Vector RAG.

        Args:
            plant_name: Name of the power plant
            targeted_content_file: Path to existing targeted content file
            extraction_info: Extraction information dictionary

        Returns:
            Tuple of (organizational_details, plant_details, extraction_info)
        """
        logger.info("🧠 Starting Vector RAG-only extraction with existing content")

        try:
            # Create empty plant details structure
            plant_data = {
                "name": plant_name,
                "plant_type": "",
                "plant_address": "",
                "lat": "",
                "long": "",
                "plant_id": 1,
                "units_id": [],
                "grid_connectivity_maps": [],
                "ppa_details": []
            }

            # Define all fields that can benefit from Vector RAG
            all_rag_fields = [
                "lat", "long", "grid_connectivity_maps", "ppa_details",
                "units_id", "plant_address", "plant_type"
            ]

            # Use Vector RAG to extract all fields
            enhanced_plant_data = await self.enhance_extraction_with_rag(
                plant_data=plant_data,
                targeted_content_file=targeted_content_file,
                plant_name=plant_name,
                missing_fields=all_rag_fields
            )

            # Track successful extractions
            rag_info = {
                "attempted_fields": all_rag_fields,
                "successful_extractions": [],
                "failed_extractions": [],
                "vector_db_created": True,
                "targeted_content_file": targeted_content_file
            }

            for field_name in all_rag_fields:
                if field_name in enhanced_plant_data and enhanced_plant_data[field_name]:
                    if enhanced_plant_data[field_name] not in ["", [], {}]:
                        rag_info["successful_extractions"].append(field_name)
                        logger.info(f"✅ Vector RAG extracted field: {field_name}")
                    else:
                        rag_info["failed_extractions"].append(field_name)
                else:
                    rag_info["failed_extractions"].append(field_name)

            extraction_info["vector_rag_extractions"] = rag_info
            extraction_info["total_pages_scraped"] = 0  # No new scraping
            extraction_info["cache_hit_fields"] = rag_info["successful_extractions"]

            # Create PlantDetails object
            plant_details = PlantDetails(**enhanced_plant_data)

            # Create minimal org details (since we're not doing org extraction)
            org_details = OrganizationalDetails(
                organization_name=enhanced_plant_data.get("name", plant_name),
                organization_type="Power Generation Company",
                organization_address="",
                organization_id=1
            )

            logger.info(f"🎉 Vector RAG-only extraction completed: {len(rag_info['successful_extractions'])} fields extracted")

            return org_details, plant_details, extraction_info

        except Exception as e:
            logger.error(f"Error in Vector RAG-only extraction: {e}")
            raise

    async def _enhance_with_vector_rag_from_fresh_content(
        self,
        plant_details: PlantDetails,
        plant_name: str,
        targeted_content_file: str,
        extraction_info: Dict
    ) -> Tuple[PlantDetails, Dict]:
        """
        Enhance plant details using Vector RAG with fresh targeted content from current run.

        Args:
            plant_details: Current plant details from standard extraction
            plant_name: Name of the plant
            targeted_content_file: Path to fresh targeted content file
            extraction_info: Extraction information dictionary

        Returns:
            Tuple of (enhanced_plant_details, rag_extraction_info)
        """
        logger.info("🧠 Starting Vector RAG enhancement with fresh targeted content")

        rag_info = {
            "attempted_fields": [],
            "successful_extractions": [],
            "failed_extractions": [],
            "vector_db_created": False,
            "targeted_content_file": targeted_content_file
        }

        try:
            # Identify missing or empty fields from the standard extraction
            missing_fields = self._identify_missing_fields(plant_details)

            if not missing_fields:
                logger.info("✅ All fields extracted by standard pipeline, no Vector RAG enhancement needed")
                return plant_details, rag_info

            logger.info(f"🎯 Fields to enhance with Vector RAG: {missing_fields}")
            rag_info["attempted_fields"] = missing_fields

            # Convert plant_details to dict for enhancement
            if hasattr(plant_details, 'model_dump'):
                plant_data = plant_details.model_dump()
            elif isinstance(plant_details, dict):
                plant_data = plant_details
            else:
                plant_data = {}

            # Use Vector RAG to enhance missing fields with fresh content
            enhanced_plant_data = await self.enhance_extraction_with_rag(
                plant_data=plant_data,
                targeted_content_file=targeted_content_file,
                plant_name=plant_name,
                missing_fields=missing_fields
            )

            # Track successful extractions
            for field_name in missing_fields:
                original_value = plant_data.get(field_name)
                enhanced_value = enhanced_plant_data.get(field_name)

                # Check if Vector RAG actually improved the field
                if enhanced_value and enhanced_value not in ["", [], {}]:
                    if not original_value or original_value in ["", [], {}] or enhanced_value != original_value:
                        rag_info["successful_extractions"].append(field_name)
                        logger.info(f"✅ Vector RAG enhanced field '{field_name}': {enhanced_value}")
                    else:
                        logger.info(f"📊 Field '{field_name}' already had good value from standard extraction")
                else:
                    rag_info["failed_extractions"].append(field_name)
                    logger.info(f"❌ Vector RAG failed to enhance field '{field_name}'")

            rag_info["vector_db_created"] = True

            # Create enhanced PlantDetails object
            enhanced_plant_details = PlantDetails(**enhanced_plant_data)

            logger.info(f"🎉 Vector RAG enhancement completed: {len(rag_info['successful_extractions'])} fields enhanced")

            return enhanced_plant_details, rag_info

        except Exception as e:
            logger.error(f"Error in Vector RAG enhancement with fresh content: {e}")
            rag_info["error"] = str(e)
            return plant_details, rag_info

    async def _enhance_with_vector_rag(
        self,
        plant_details: PlantDetails,
        plant_name: str,
        extraction_info: Dict
    ) -> Tuple[PlantDetails, Dict]:
        """
        Enhance plant details extraction using Vector RAG.

        Args:
            plant_details: Current plant details
            plant_name: Name of the plant
            extraction_info: Extraction information dictionary

        Returns:
            Tuple of (enhanced_plant_details, rag_extraction_info)
        """
        logger.info("🧠 Starting Vector RAG enhancement phase")

        rag_info = {
            "attempted_fields": [],
            "successful_extractions": [],
            "failed_extractions": [],
            "vector_db_created": False,
            "targeted_content_file": None
        }

        try:
            # Find the most recent targeted_content file
            targeted_content_file = self._find_targeted_content_file(plant_name)

            if not targeted_content_file:
                logger.warning("No targeted_content file found, skipping Vector RAG enhancement")
                return plant_details, rag_info

            rag_info["targeted_content_file"] = targeted_content_file
            logger.info(f"📁 Using targeted content file: {targeted_content_file}")

            # Identify missing or empty fields
            missing_fields = self._identify_missing_fields(plant_details)

            if not missing_fields:
                logger.info("No missing fields found, skipping Vector RAG enhancement")
                return plant_details, rag_info

            logger.info(f"🎯 Fields to enhance with Vector RAG: {missing_fields}")
            rag_info["attempted_fields"] = missing_fields

            # Convert plant_details to dict for enhancement
            if hasattr(plant_details, 'model_dump'):
                plant_data = plant_details.model_dump()
            elif isinstance(plant_details, dict):
                plant_data = plant_details
            else:
                plant_data = {}

            # Enhance using Vector RAG
            enhanced_plant_data = await self.enhance_extraction_with_rag(
                plant_data=plant_data,
                targeted_content_file=targeted_content_file,
                plant_name=plant_name,
                missing_fields=missing_fields
            )

            # Track successful extractions
            for field_name in missing_fields:
                if field_name in enhanced_plant_data and enhanced_plant_data[field_name]:
                    if not plant_data.get(field_name) or plant_data[field_name] != enhanced_plant_data[field_name]:
                        rag_info["successful_extractions"].append(field_name)
                        logger.info(f"✅ Vector RAG enhanced field: {field_name}")
                    else:
                        rag_info["failed_extractions"].append(field_name)

            rag_info["vector_db_created"] = True

            # Create enhanced PlantDetails object
            enhanced_plant_details = PlantDetails(**enhanced_plant_data)

            logger.info(f"🎉 Vector RAG enhancement completed: {len(rag_info['successful_extractions'])} fields enhanced")

            return enhanced_plant_details, rag_info

        except Exception as e:
            logger.error(f"Error in Vector RAG enhancement: {e}")
            rag_info["error"] = str(e)
            return plant_details, rag_info

    def _find_targeted_content_file(self, plant_name: str) -> Optional[str]:
        """Find the most recent targeted_content file for the plant."""

        # Look for files matching the pattern
        plant_id = plant_name.lower().replace(" ", "_")
        pattern = f"{plant_id}_targeted_content_"

        # Search in current directory
        matching_files = []
        for filename in os.listdir("."):
            if filename.startswith(pattern) and filename.endswith(".json"):
                matching_files.append(filename)

        if not matching_files:
            return None

        # Return the most recent file (by filename timestamp)
        matching_files.sort(reverse=True)
        return matching_files[0]

    def _identify_missing_fields(self, plant_details) -> List[str]:
        """
        Identify fields that are missing or empty in plant details.

        Args:
            plant_details: Current plant details (PlantDetails object or dict)

        Returns:
            List of field names that need enhancement
        """
        missing_fields = []

        # Handle both PlantDetails objects and dictionaries
        if hasattr(plant_details, 'model_dump'):
            plant_data = plant_details.model_dump()
        elif isinstance(plant_details, dict):
            plant_data = plant_details
        else:
            return []  # Can't process unknown types

        # Define fields that can benefit from Vector RAG
        rag_capable_fields = [
            "lat", "long", "grid_connectivity_maps", "ppa_details",
            "units_id", "plant_address", "name"
        ]

        for field_name in rag_capable_fields:
            field_value = plant_data.get(field_name)

            # Check if field is missing or empty
            if field_value is None or field_value == "" or field_value == []:
                missing_fields.append(field_name)
            # For string fields, check if they contain placeholder values
            elif isinstance(field_value, str) and field_value.lower() in ["unknown", "not found", "n/a"]:
                missing_fields.append(field_name)

        return missing_fields

    async def save_results(
        self,
        org_details: Optional[OrganizationalDetails],
        plant_details: Optional[PlantDetails],
        extraction_info: Optional[Dict],
        org_output_path: str = "org_details_vector_rag.json",
        plant_output_path: str = "plant_details_vector_rag.json",
        info_output_path: str = "extraction_info_vector_rag.json"
    ):
        """Save extraction results with Vector RAG information."""

        # Call parent save method
        await super().save_results(
            org_details, plant_details, extraction_info,
            org_output_path, plant_output_path, info_output_path
        )

        # Save additional Vector RAG statistics
        if extraction_info and self.enable_vector_rag:
            rag_stats = {
                "vector_rag_stats": extraction_info.get("vector_rag_extractions", {}),
                "rag_system_info": self.get_rag_stats(),
                "timestamp": datetime.now().isoformat()
            }

            rag_stats_path = "vector_rag_stats.json"
            import json
            with open(rag_stats_path, 'w', encoding='utf-8') as f:
                json.dump(rag_stats, f, indent=2, ensure_ascii=False)

            logger.info(f"Vector RAG statistics saved to {rag_stats_path}")


# Convenience function for easy usage
async def extract_with_vector_rag(
    plant_name: str,
    enable_rag: bool = True,
    use_openai: bool = False,
    use_existing_content: bool = True
):
    """
    Convenience function to extract plant data with Vector RAG.

    Args:
        plant_name: Name of the power plant
        enable_rag: Whether to enable Vector RAG enhancement
        use_openai: Whether to use OpenAI instead of Groq
        use_existing_content: Whether to use existing targeted content first

    Returns:
        Tuple of (org_details, plant_details, extraction_info)
    """
    pipeline = VectorRAGPowerPlantPipeline(enable_vector_rag=enable_rag, use_openai=use_openai)
    return await pipeline.extract_plant_data(plant_name, use_existing_content=use_existing_content)
