2025-05-28 23:55:50,503 - src.vector_rag.vectorizer - INFO - Building vector databases from: jhajjar_power_plant_targeted_content_20250528_232641.json
2025-05-28 23:55:50,504 - src.vector_rag.vectorizer - INFO - Processing field: lat (3 URLs)
2025-05-28 23:55:50,505 - src.vector_rag.vectorizer - INFO - Generated 21 chunks for field lat
2025-05-28 23:55:50,505 - src.vector_rag.vectorizer - INFO - Loading embedding model: all-MiniLM-L6-v2
2025-05-28 23:55:50,549 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-05-28 23:55:50,549 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-05-28 23:55:54,694 - src.vector_rag.vectorizer - INFO - Embedding model loaded successfully
2025-05-28 23:55:54,694 - src.vector_rag.vectorizer - INFO - Generating embeddings for 21 chunks...
2025-05-28 23:55:55,498 - src.vector_rag.vectorizer - INFO - ✅ Created vector store for lat: 21 chunks
2025-05-28 23:55:55,498 - src.vector_rag.vectorizer - INFO - Processing field: long (3 URLs)
2025-05-28 23:55:55,499 - src.vector_rag.vectorizer - INFO - Generated 21 chunks for field long
2025-05-28 23:55:55,499 - src.vector_rag.vectorizer - INFO - Generating embeddings for 21 chunks...
2025-05-28 23:55:55,546 - src.vector_rag.vectorizer - INFO - ✅ Created vector store for long: 21 chunks
2025-05-28 23:55:55,546 - src.vector_rag.vectorizer - INFO - Processing field: grid_connectivity_maps (3 URLs)
2025-05-28 23:55:55,546 - src.vector_rag.vectorizer - INFO - Generated 116 chunks for field grid_connectivity_maps
2025-05-28 23:55:55,546 - src.vector_rag.vectorizer - INFO - Generating embeddings for 116 chunks...
2025-05-28 23:55:56,329 - src.vector_rag.vectorizer - INFO - ✅ Created vector store for grid_connectivity_maps: 116 chunks
2025-05-28 23:55:56,329 - src.vector_rag.vectorizer - INFO - Processing field: ppa_details (3 URLs)
2025-05-28 23:55:56,329 - src.vector_rag.vectorizer - INFO - Generated 80 chunks for field ppa_details
2025-05-28 23:55:56,329 - src.vector_rag.vectorizer - INFO - Generating embeddings for 80 chunks...
2025-05-28 23:55:56,868 - src.vector_rag.vectorizer - INFO - ✅ Created vector store for ppa_details: 80 chunks
2025-05-28 23:55:56,868 - src.vector_rag.vectorizer - INFO - Processing field: units_id (3 URLs)
2025-05-28 23:55:56,868 - src.vector_rag.field_chunkers - WARNING - No specific chunking config for field 'units_id', using default
2025-05-28 23:55:56,868 - src.vector_rag.field_chunkers - WARNING - No specific chunking config for field 'units_id', using default
2025-05-28 23:55:56,868 - src.vector_rag.field_chunkers - WARNING - No specific chunking config for field 'units_id', using default
2025-05-28 23:55:56,868 - src.vector_rag.vectorizer - INFO - Generated 147 chunks for field units_id
2025-05-28 23:55:56,868 - src.vector_rag.vectorizer - INFO - Generating embeddings for 147 chunks...
2025-05-28 23:55:58,157 - src.vector_rag.vectorizer - INFO - ✅ Created vector store for units_id: 147 chunks
2025-05-28 23:55:58,157 - src.vector_rag.vectorizer - INFO - Vector databases created successfully in: test_vector_db/jhajjar_power_plant_20250528_232641
2025-05-28 23:55:58,158 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: lat
2025-05-28 23:55:58,158 - src.vector_rag.rag_extractor - INFO - Loading embedding model: all-MiniLM-L6-v2
2025-05-28 23:55:58,160 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-05-28 23:55:58,160 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-05-28 23:56:01,414 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: long
2025-05-28 23:56:01,428 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: grid_connectivity_maps
2025-05-28 23:56:01,519 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: ppa_details
2025-05-28 23:56:01,559 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: units_id
