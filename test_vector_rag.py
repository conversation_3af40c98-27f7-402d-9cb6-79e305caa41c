#!/usr/bin/env python3
"""
Test script for the Vector RAG implementation.

This script tests the vector database creation and RAG extraction
using the actual targeted_content JSON file.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from src.vector_rag.vectorizer import TargetedContentVectorizer
    from src.vector_rag.rag_extractor import VectorRAGExtractor
    from src.vector_rag.field_chunkers import FieldSpecificChunker
    print("✅ Vector RAG modules imported successfully")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install required dependencies:")
    print("pip install faiss-cpu sentence-transformers numpy")
    sys.exit(1)


def setup_logging():
    """Configure logging for the test."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('test_vector_rag.log'),
            logging.StreamHandler()
        ]
    )


async def test_chunking():
    """Test the field-specific chunking functionality."""
    print("\n🧪 Testing Field-Specific Chunking")
    print("=" * 50)
    
    chunker = FieldSpecificChunker()
    
    # Test with sample content
    sample_content = """
    Jhajjar Power Plant is located at coordinates 28.607111, 76.656914.
    The plant connects to the grid through 400 kV transmission lines.
    Power transmission lines connect to substations at Sonipat and Mahendragarh.
    The power purchase agreement covers 1320 MW capacity for 25 years.
    """
    
    for field_name in ["lat", "long", "grid_connectivity_maps", "ppa_details"]:
        chunks = chunker.chunk_content_for_field(field_name, sample_content)
        print(f"Field '{field_name}': {len(chunks)} chunks")
        for i, chunk in enumerate(chunks[:2]):  # Show first 2 chunks
            print(f"  Chunk {i+1}: {chunk[:100]}...")
    
    print("✅ Chunking test completed")


async def test_vectorizer():
    """Test vector database creation from actual targeted_content file."""
    print("\n🧪 Testing Vector Database Creation")
    print("=" * 50)
    
    # Find the targeted_content file
    targeted_content_file = "jhajjar_power_plant_targeted_content_20250528_232641.json"
    
    if not os.path.exists(targeted_content_file):
        print(f"❌ Targeted content file not found: {targeted_content_file}")
        return False
    
    print(f"📁 Using file: {targeted_content_file}")
    
    try:
        # Create vectorizer
        vectorizer = TargetedContentVectorizer()
        
        # Build vector databases
        vector_info = await vectorizer.build_vector_db_from_targeted_content(
            targeted_content_file,
            output_dir="test_vector_db"
        )
        
        print(f"✅ Vector databases created for plant: {vector_info['plant_name']}")
        print(f"📊 Fields processed: {list(vector_info['fields'].keys())}")
        
        for field_name, field_info in vector_info['fields'].items():
            print(f"  - {field_name}: {field_info['chunk_count']} chunks")
        
        return True
        
    except Exception as e:
        print(f"❌ Vector database creation failed: {e}")
        logging.error(f"Vectorizer test failed: {e}", exc_info=True)
        return False


async def test_rag_extraction():
    """Test RAG-based extraction."""
    print("\n🧪 Testing RAG Extraction")
    print("=" * 50)
    
    # Check if vector database exists
    vector_db_path = "test_vector_db/jhajjar_power_plant_20250528_232641"
    
    if not os.path.exists(vector_db_path):
        print(f"❌ Vector database not found: {vector_db_path}")
        print("Please run vector database creation first")
        return False
    
    try:
        # Create RAG extractor (without LLM client for now)
        extractor = VectorRAGExtractor(vector_db_path)
        
        # Test metadata loading
        if extractor.metadata:
            print(f"✅ Metadata loaded for plant: {extractor.metadata['plant_name']}")
            print(f"📊 Available fields: {extractor.get_available_fields()}")
        else:
            print("❌ Failed to load metadata")
            return False
        
        # Test vector similarity search for each field
        plant_name = "Jhajjar Power Plant"
        
        for field_name in extractor.get_available_fields():
            print(f"\n🔍 Testing similarity search for: {field_name}")
            
            # Get vector store
            vector_store = extractor._get_field_vector_store(field_name)
            if vector_store:
                print(f"  ✅ Vector store loaded: {len(vector_store['chunks'])} chunks")
                
                # Test query construction
                query = extractor._construct_field_query(field_name, plant_name)
                print(f"  🔍 Query: '{query}'")
                
                # Test similarity search (without LLM extraction)
                try:
                    relevant_chunks = extractor._vector_similarity_search(
                        vector_store, query, top_k=3, score_threshold=0.5
                    )
                    
                    print(f"  📊 Found {len(relevant_chunks)} relevant chunks")
                    for i, chunk_data in enumerate(relevant_chunks[:2]):
                        score = chunk_data["similarity_score"]
                        preview = chunk_data["chunk_text"][:100]
                        print(f"    Chunk {i+1} (score: {score:.3f}): {preview}...")
                        
                except Exception as e:
                    print(f"  ❌ Similarity search failed: {e}")
            else:
                print(f"  ❌ Failed to load vector store for {field_name}")
        
        print("✅ RAG extraction test completed")
        return True
        
    except Exception as e:
        print(f"❌ RAG extraction test failed: {e}")
        logging.error(f"RAG extraction test failed: {e}", exc_info=True)
        return False


async def main():
    """Run all tests."""
    setup_logging()
    
    print("🚀 Vector RAG Implementation Test")
    print("=" * 60)
    
    # Test 1: Chunking
    await test_chunking()
    
    # Test 2: Vector Database Creation
    vectorizer_success = await test_vectorizer()
    
    # Test 3: RAG Extraction (only if vectorizer succeeded)
    if vectorizer_success:
        await test_rag_extraction()
    
    print("\n" + "=" * 60)
    print("🏁 All tests completed!")
    print("Check 'test_vector_rag.log' for detailed logs")
    print("Vector databases saved in 'test_vector_db/' directory")


if __name__ == "__main__":
    asyncio.run(main())
