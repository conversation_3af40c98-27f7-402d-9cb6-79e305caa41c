2025-05-28 23:59:30,977 - src.vector_rag.rag_extractor - INFO - Starting RAG extraction for field: lat
2025-05-28 23:59:30,978 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: lat
2025-05-28 23:59:30,978 - src.vector_rag.rag_extractor - INFO - RAG query: 'Jhajjar Power Plant latitude coordinates GPS location decimal degrees'
2025-05-28 23:59:30,978 - src.vector_rag.rag_extractor - INFO - Loading embedding model: all-MiniLM-L6-v2
2025-05-28 23:59:30,993 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-05-28 23:59:30,993 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-05-28 23:59:35,042 - src.vector_rag.rag_extractor - INFO - Found 3 relevant chunks for lat
2025-05-28 23:59:35,159 - src.vector_rag.rag_extractor - INFO - Starting RAG extraction for field: long
2025-05-28 23:59:35,160 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: long
2025-05-28 23:59:35,160 - src.vector_rag.rag_extractor - INFO - RAG query: 'Jhajjar Power Plant longitude coordinates GPS location decimal degrees'
2025-05-28 23:59:35,173 - src.vector_rag.rag_extractor - INFO - Found 3 relevant chunks for long
2025-05-28 23:59:35,173 - src.vector_rag.rag_extractor - INFO - Starting RAG extraction for field: grid_connectivity_maps
2025-05-28 23:59:35,175 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: grid_connectivity_maps
2025-05-28 23:59:35,175 - src.vector_rag.rag_extractor - INFO - RAG query: 'Jhajjar Power Plant substation transmission grid connectivity voltage kV switchyard HVPNL'
2025-05-28 23:59:35,212 - src.vector_rag.rag_extractor - INFO - Found 3 relevant chunks for grid_connectivity_maps
2025-05-28 23:59:35,212 - src.vector_rag.rag_extractor - WARNING - Could not load prompts from config: '\n  "details"'
2025-05-28 23:59:35,212 - src.vector_rag.rag_extractor - INFO - Starting RAG extraction for field: ppa_details
2025-05-28 23:59:35,213 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: ppa_details
2025-05-28 23:59:35,213 - src.vector_rag.rag_extractor - INFO - RAG query: 'Jhajjar Power Plant power purchase agreement PPA contract pricing capacity MW distribution utility'
2025-05-28 23:59:35,246 - src.vector_rag.rag_extractor - INFO - Found 3 relevant chunks for ppa_details
2025-05-28 23:59:35,246 - src.vector_rag.rag_extractor - WARNING - Could not load prompts from config: '\n  "capacity"'
2025-05-28 23:59:35,246 - src.vector_rag.rag_extractor - INFO - Starting RAG extraction for field: units_id
2025-05-28 23:59:35,248 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: units_id
2025-05-28 23:59:35,248 - src.vector_rag.rag_extractor - INFO - RAG query: 'Jhajjar Power Plant units turbines generators capacity MW operational'
2025-05-28 23:59:35,260 - src.vector_rag.rag_extractor - INFO - Found 3 relevant chunks for units_id
2025-05-28 23:59:35,261 - src.vector_rag.rag_extractor - INFO - Starting RAG extraction for field: lat
2025-05-28 23:59:35,261 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: lat
2025-05-28 23:59:35,261 - src.vector_rag.rag_extractor - INFO - RAG query: 'Jhajjar Power Plant latitude coordinates GPS location decimal degrees'
2025-05-28 23:59:35,261 - src.vector_rag.rag_extractor - INFO - Loading embedding model: all-MiniLM-L6-v2
2025-05-28 23:59:35,262 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-05-28 23:59:35,262 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-05-28 23:59:38,542 - src.vector_rag.rag_extractor - INFO - Found 3 relevant chunks for lat
2025-05-28 23:59:38,542 - src.vector_rag.rag_extractor - ERROR - Unsupported LLM client type: <class '__main__.MockOpenAIClient'>
2025-05-28 23:59:38,542 - src.vector_rag.rag_extractor - WARNING - Empty response from LLM for field lat
2025-05-28 23:59:38,542 - src.vector_rag.rag_extractor - INFO - Starting RAG extraction for field: long
2025-05-28 23:59:38,543 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: long
2025-05-28 23:59:38,543 - src.vector_rag.rag_extractor - INFO - RAG query: 'Jhajjar Power Plant longitude coordinates GPS location decimal degrees'
2025-05-28 23:59:38,556 - src.vector_rag.rag_extractor - INFO - Found 3 relevant chunks for long
2025-05-28 23:59:38,556 - src.vector_rag.rag_extractor - ERROR - Unsupported LLM client type: <class '__main__.MockOpenAIClient'>
2025-05-28 23:59:38,556 - src.vector_rag.rag_extractor - WARNING - Empty response from LLM for field long
2025-05-28 23:59:38,556 - src.vector_rag.rag_extractor - INFO - Starting RAG extraction for field: grid_connectivity_maps
2025-05-28 23:59:38,557 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: grid_connectivity_maps
2025-05-28 23:59:38,557 - src.vector_rag.rag_extractor - INFO - RAG query: 'Jhajjar Power Plant substation transmission grid connectivity voltage kV switchyard HVPNL'
2025-05-28 23:59:38,566 - src.vector_rag.rag_extractor - INFO - Found 3 relevant chunks for grid_connectivity_maps
2025-05-28 23:59:38,566 - src.vector_rag.rag_extractor - WARNING - Could not load prompts from config: '\n  "details"'
2025-05-28 23:59:38,566 - src.vector_rag.rag_extractor - ERROR - Unsupported LLM client type: <class '__main__.MockOpenAIClient'>
2025-05-28 23:59:38,566 - src.vector_rag.rag_extractor - WARNING - Empty response from LLM for field grid_connectivity_maps
2025-05-28 23:59:38,566 - src.vector_rag.rag_extractor - INFO - Starting RAG extraction for field: ppa_details
2025-05-28 23:59:38,567 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: ppa_details
2025-05-28 23:59:38,567 - src.vector_rag.rag_extractor - INFO - RAG query: 'Jhajjar Power Plant power purchase agreement PPA contract pricing capacity MW distribution utility'
2025-05-28 23:59:38,577 - src.vector_rag.rag_extractor - INFO - Found 3 relevant chunks for ppa_details
2025-05-28 23:59:38,577 - src.vector_rag.rag_extractor - WARNING - Could not load prompts from config: '\n  "capacity"'
2025-05-28 23:59:38,577 - src.vector_rag.rag_extractor - ERROR - Unsupported LLM client type: <class '__main__.MockOpenAIClient'>
2025-05-28 23:59:38,577 - src.vector_rag.rag_extractor - WARNING - Empty response from LLM for field ppa_details
2025-05-28 23:59:38,577 - src.vector_rag.rag_extractor - INFO - Starting RAG extraction for field: units_id
2025-05-28 23:59:38,578 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: units_id
2025-05-28 23:59:38,578 - src.vector_rag.rag_extractor - INFO - RAG query: 'Jhajjar Power Plant units turbines generators capacity MW operational'
2025-05-28 23:59:38,590 - src.vector_rag.rag_extractor - INFO - Found 3 relevant chunks for units_id
2025-05-28 23:59:38,591 - src.vector_rag.rag_extractor - ERROR - Unsupported LLM client type: <class '__main__.MockOpenAIClient'>
2025-05-28 23:59:38,591 - src.vector_rag.rag_extractor - WARNING - Empty response from LLM for field units_id
2025-05-28 23:59:38,591 - src.vector_rag.pipeline_integration - INFO - 🚀 Starting Vector RAG processing for Jhajjar Power Plant
2025-05-28 23:59:38,591 - src.vector_rag.pipeline_integration - INFO - 📁 Targeted content file: jhajjar_power_plant_targeted_content_20250528_232641.json
2025-05-28 23:59:38,591 - src.vector_rag.pipeline_integration - INFO - 🎯 Missing fields to extract: ['lat', 'long', 'grid_connectivity_maps', 'ppa_details']
2025-05-28 23:59:38,591 - src.vector_rag.pipeline_integration - INFO - 🔨 Creating vector databases from targeted content...
2025-05-28 23:59:38,591 - src.vector_rag.vectorizer - INFO - Building vector databases from: jhajjar_power_plant_targeted_content_20250528_232641.json
2025-05-28 23:59:38,592 - src.vector_rag.vectorizer - INFO - Processing field: lat (3 URLs)
2025-05-28 23:59:38,593 - src.vector_rag.vectorizer - INFO - Generated 21 chunks for field lat
2025-05-28 23:59:38,593 - src.vector_rag.vectorizer - INFO - Loading embedding model: all-MiniLM-L6-v2
2025-05-28 23:59:38,594 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-05-28 23:59:38,594 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-05-28 23:59:44,732 - src.vector_rag.vectorizer - INFO - Embedding model loaded successfully
2025-05-28 23:59:44,732 - src.vector_rag.vectorizer - INFO - Generating embeddings for 21 chunks...
2025-05-28 23:59:44,848 - src.vector_rag.vectorizer - INFO - ✅ Created vector store for lat: 21 chunks
2025-05-28 23:59:44,848 - src.vector_rag.vectorizer - INFO - Processing field: long (3 URLs)
2025-05-28 23:59:44,848 - src.vector_rag.vectorizer - INFO - Generated 21 chunks for field long
2025-05-28 23:59:44,849 - src.vector_rag.vectorizer - INFO - Generating embeddings for 21 chunks...
2025-05-28 23:59:44,895 - src.vector_rag.vectorizer - INFO - ✅ Created vector store for long: 21 chunks
2025-05-28 23:59:44,895 - src.vector_rag.vectorizer - INFO - Processing field: grid_connectivity_maps (3 URLs)
2025-05-28 23:59:44,895 - src.vector_rag.vectorizer - INFO - Generated 116 chunks for field grid_connectivity_maps
2025-05-28 23:59:44,895 - src.vector_rag.vectorizer - INFO - Generating embeddings for 116 chunks...
2025-05-28 23:59:45,678 - src.vector_rag.vectorizer - INFO - ✅ Created vector store for grid_connectivity_maps: 116 chunks
2025-05-28 23:59:45,679 - src.vector_rag.vectorizer - INFO - Processing field: ppa_details (3 URLs)
2025-05-28 23:59:45,679 - src.vector_rag.vectorizer - INFO - Generated 80 chunks for field ppa_details
2025-05-28 23:59:45,679 - src.vector_rag.vectorizer - INFO - Generating embeddings for 80 chunks...
2025-05-28 23:59:46,213 - src.vector_rag.vectorizer - INFO - ✅ Created vector store for ppa_details: 80 chunks
2025-05-28 23:59:46,213 - src.vector_rag.vectorizer - INFO - Processing field: units_id (3 URLs)
2025-05-28 23:59:46,213 - src.vector_rag.field_chunkers - WARNING - No specific chunking config for field 'units_id', using default
2025-05-28 23:59:46,213 - src.vector_rag.field_chunkers - WARNING - No specific chunking config for field 'units_id', using default
2025-05-28 23:59:46,213 - src.vector_rag.field_chunkers - WARNING - No specific chunking config for field 'units_id', using default
2025-05-28 23:59:46,213 - src.vector_rag.vectorizer - INFO - Generated 147 chunks for field units_id
2025-05-28 23:59:46,213 - src.vector_rag.vectorizer - INFO - Generating embeddings for 147 chunks...
2025-05-28 23:59:47,117 - src.vector_rag.vectorizer - INFO - ✅ Created vector store for units_id: 147 chunks
2025-05-28 23:59:47,118 - src.vector_rag.vectorizer - INFO - Vector databases created successfully in: test_vector_db_integration/jhajjar_power_plant_20250528_232641
2025-05-28 23:59:47,118 - src.vector_rag.pipeline_integration - INFO - ✅ Vector databases created: ['lat', 'long', 'grid_connectivity_maps', 'ppa_details', 'units_id']
2025-05-28 23:59:47,118 - src.vector_rag.pipeline_integration - INFO - 🔍 Extracting field 'lat' using Vector RAG...
2025-05-28 23:59:47,118 - src.vector_rag.rag_extractor - INFO - Starting RAG extraction for field: lat
2025-05-28 23:59:47,118 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: lat
2025-05-28 23:59:47,118 - src.vector_rag.rag_extractor - INFO - RAG query: 'Jhajjar Power Plant latitude coordinates GPS location decimal degrees'
2025-05-28 23:59:47,118 - src.vector_rag.rag_extractor - INFO - Loading embedding model: all-MiniLM-L6-v2
2025-05-28 23:59:47,121 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-05-28 23:59:47,121 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-05-28 23:59:50,302 - src.vector_rag.rag_extractor - INFO - Found 3 relevant chunks for lat
2025-05-28 23:59:50,302 - src.vector_rag.pipeline_integration - INFO - ✅ Successfully extracted lat
2025-05-28 23:59:50,302 - src.vector_rag.pipeline_integration - INFO - 🔍 Extracting field 'long' using Vector RAG...
2025-05-28 23:59:50,302 - src.vector_rag.rag_extractor - INFO - Starting RAG extraction for field: long
2025-05-28 23:59:50,303 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: long
2025-05-28 23:59:50,303 - src.vector_rag.rag_extractor - INFO - RAG query: 'Jhajjar Power Plant longitude coordinates GPS location decimal degrees'
2025-05-28 23:59:50,315 - src.vector_rag.rag_extractor - INFO - Found 3 relevant chunks for long
2025-05-28 23:59:50,315 - src.vector_rag.pipeline_integration - INFO - ✅ Successfully extracted long
2025-05-28 23:59:50,315 - src.vector_rag.pipeline_integration - INFO - 🔍 Extracting field 'grid_connectivity_maps' using Vector RAG...
2025-05-28 23:59:50,315 - src.vector_rag.rag_extractor - INFO - Starting RAG extraction for field: grid_connectivity_maps
2025-05-28 23:59:50,316 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: grid_connectivity_maps
2025-05-28 23:59:50,316 - src.vector_rag.rag_extractor - INFO - RAG query: 'Jhajjar Power Plant substation transmission grid connectivity voltage kV switchyard HVPNL'
2025-05-28 23:59:50,326 - src.vector_rag.rag_extractor - INFO - Found 3 relevant chunks for grid_connectivity_maps
2025-05-28 23:59:50,326 - src.vector_rag.rag_extractor - WARNING - Could not load prompts from config: '\n  "details"'
2025-05-28 23:59:50,326 - src.vector_rag.pipeline_integration - INFO - ✅ Successfully extracted grid_connectivity_maps
2025-05-28 23:59:50,326 - src.vector_rag.pipeline_integration - INFO - 🔍 Extracting field 'ppa_details' using Vector RAG...
2025-05-28 23:59:50,326 - src.vector_rag.rag_extractor - INFO - Starting RAG extraction for field: ppa_details
2025-05-28 23:59:50,327 - src.vector_rag.rag_extractor - INFO - Loaded vector store for field: ppa_details
2025-05-28 23:59:50,327 - src.vector_rag.rag_extractor - INFO - RAG query: 'Jhajjar Power Plant power purchase agreement PPA contract pricing capacity MW distribution utility'
2025-05-28 23:59:50,336 - src.vector_rag.rag_extractor - INFO - Found 3 relevant chunks for ppa_details
2025-05-28 23:59:50,336 - src.vector_rag.rag_extractor - WARNING - Could not load prompts from config: '\n  "capacity"'
2025-05-28 23:59:50,336 - src.vector_rag.pipeline_integration - INFO - ✅ Successfully extracted ppa_details
2025-05-28 23:59:50,336 - src.vector_rag.pipeline_integration - INFO - ✅ Vector RAG processing completed. Extracted 4 fields
2025-05-28 23:59:50,873 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with Groq
2025-05-28 23:59:50,887 - src.plant_details_extractor - INFO - Plant details extractor initialized with Groq
2025-05-28 23:59:50,888 - src.vector_rag.pipeline_integration - INFO - ✅ Vector RAG integration enabled
2025-05-28 23:59:50,888 - src.vector_rag_pipeline - INFO - Vector RAG Enhanced Pipeline initialized (RAG enabled: True)
