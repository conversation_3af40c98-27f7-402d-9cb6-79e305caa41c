#!/usr/bin/env python3
"""
Test script for Vector RAG LLM integration.

This script tests the complete Vector RAG pipeline including LLM integration
using the actual targeted_content JSON file and mock LLM clients.
"""

import asyncio
import logging
import sys
import os
import json
from pathlib import Path
from typing import Dict, Any, Optional

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from src.vector_rag.vectorizer import TargetedContentVectorizer
    from src.vector_rag.rag_extractor import VectorRAGExtractor
    from src.vector_rag.pipeline_integration import VectorRAGPipelineIntegration
    print("✅ Vector RAG modules imported successfully")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install required dependencies:")
    print("pip install faiss-cpu sentence-transformers numpy")
    sys.exit(1)


class MockLLMClient:
    """Mock LLM client for testing purposes."""
    
    def __init__(self, client_type: str = "openai"):
        """
        Initialize mock LLM client.
        
        Args:
            client_type: Type of client to mock ("openai" or "groq")
        """
        self.client_type = client_type
        self.call_count = 0
        
    async def extract_field(self, prompt: str) -> str:
        """Mock extract_field method for Groq-style clients."""
        self.call_count += 1
        
        # Simulate field extraction based on prompt content
        if "lat" in prompt.lower() and "coordinate" in prompt.lower():
            return "28.607111"
        elif "long" in prompt.lower() and "coordinate" in prompt.lower():
            return "76.656914"
        elif "grid_connectivity" in prompt.lower():
            return '[{"substation_name": "Sonipat", "voltage_level": "400 kV", "distance_km": 25}, {"substation_name": "Mahendragarh", "voltage_level": "400 kV", "distance_km": 30}]'
        elif "ppa_details" in prompt.lower():
            return '[{"buyer": "Haryana Distribution Utility", "capacity_mw": 1320, "tenure_years": 25, "contract_type": "Long-term PPA"}]'
        elif "units_id" in prompt.lower():
            return '[{"unit_id": "Unit 1", "capacity_mw": 660, "technology": "Coal", "status": "Operational"}, {"unit_id": "Unit 2", "capacity_mw": 660, "technology": "Coal", "status": "Operational"}]'
        else:
            return "Information not found in the provided content."


class MockOpenAIClient:
    """Mock OpenAI client for testing."""
    
    def __init__(self):
        self.chat = MockChatCompletions()


class MockChatCompletions:
    """Mock chat completions for OpenAI client."""
    
    def __init__(self):
        self.call_count = 0
    
    async def create(self, **kwargs):
        """Mock create method."""
        self.call_count += 1
        
        # Extract prompt from messages
        messages = kwargs.get("messages", [])
        prompt = ""
        for message in messages:
            if message.get("role") == "user":
                prompt = message.get("content", "")
                break
        
        # Mock response based on prompt
        mock_client = MockLLMClient()
        content = await mock_client.extract_field(prompt)
        
        return MockResponse(content)


class MockResponse:
    """Mock response object."""
    
    def __init__(self, content: str):
        self.choices = [MockChoice(content)]


class MockChoice:
    """Mock choice object."""
    
    def __init__(self, content: str):
        self.message = MockMessage(content)


class MockMessage:
    """Mock message object."""
    
    def __init__(self, content: str):
        self.content = content


def setup_logging():
    """Configure logging for the test."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('test_vector_rag_llm.log'),
            logging.StreamHandler()
        ]
    )


async def test_rag_extractor_with_mock_llm():
    """Test RAG extractor with mock LLM clients."""
    print("\n🧪 Testing RAG Extractor with Mock LLM")
    print("=" * 50)
    
    # Check if vector database exists
    vector_db_path = "test_vector_db/jhajjar_power_plant_20250528_232641"
    
    if not os.path.exists(vector_db_path):
        print(f"❌ Vector database not found: {vector_db_path}")
        print("Please run the basic vector test first to create the database")
        return False
    
    # Test with different mock LLM clients
    test_clients = {
        "Mock Groq Client": MockLLMClient("groq"),
        "Mock OpenAI Client": MockOpenAIClient()
    }
    
    plant_name = "Jhajjar Power Plant"
    test_fields = ["lat", "long", "grid_connectivity_maps", "ppa_details", "units_id"]
    
    for client_name, mock_client in test_clients.items():
        print(f"\n🔧 Testing with {client_name}")
        
        try:
            # Create RAG extractor with mock LLM
            extractor = VectorRAGExtractor(
                vector_db_path=vector_db_path,
                llm_client=mock_client
            )
            
            print(f"✅ RAG extractor created with {client_name}")
            
            # Test extraction for each field
            for field_name in test_fields:
                print(f"\n🎯 Testing extraction for field: {field_name}")
                
                try:
                    extracted_value = await extractor.extract_field_with_rag(
                        field_name=field_name,
                        plant_name=plant_name,
                        top_k=3,
                        score_threshold=0.5
                    )
                    
                    if extracted_value is not None:
                        print(f"  ✅ Extracted: {str(extracted_value)[:100]}...")
                    else:
                        print(f"  ❌ No value extracted")
                        
                except Exception as e:
                    print(f"  ❌ Extraction failed: {e}")
            
            print(f"✅ {client_name} testing completed")
            
        except Exception as e:
            print(f"❌ {client_name} testing failed: {e}")
            return False
    
    return True


async def test_pipeline_integration():
    """Test the full pipeline integration."""
    print("\n🧪 Testing Pipeline Integration")
    print("=" * 50)
    
    # Find targeted content file
    targeted_content_file = "jhajjar_power_plant_targeted_content_20250528_232641.json"
    
    if not os.path.exists(targeted_content_file):
        print(f"❌ Targeted content file not found: {targeted_content_file}")
        return False
    
    try:
        # Create mock LLM client
        mock_llm = MockLLMClient("groq")
        
        # Create pipeline integration
        pipeline_integration = VectorRAGPipelineIntegration(
            llm_client=mock_llm,
            vector_db_base_path="test_vector_db_integration"
        )
        
        print("✅ Pipeline integration created")
        
        # Test full processing
        plant_name = "Jhajjar Power Plant"
        missing_fields = ["lat", "long", "grid_connectivity_maps", "ppa_details"]
        
        print(f"🚀 Processing targeted content with RAG...")
        
        extracted_fields = await pipeline_integration.process_targeted_content_with_rag(
            targeted_content_file=targeted_content_file,
            plant_name=plant_name,
            missing_fields=missing_fields
        )
        
        print(f"✅ Pipeline processing completed")
        print(f"📊 Extracted fields: {list(extracted_fields.keys())}")
        
        for field_name, value in extracted_fields.items():
            print(f"  - {field_name}: {str(value)[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline integration test failed: {e}")
        logging.error(f"Pipeline integration test failed: {e}", exc_info=True)
        return False


async def test_vector_rag_pipeline():
    """Test the complete Vector RAG pipeline."""
    print("\n🧪 Testing Complete Vector RAG Pipeline")
    print("=" * 50)
    
    try:
        # Import the Vector RAG pipeline
        from src.vector_rag_pipeline import VectorRAGPowerPlantPipeline
        
        print("✅ Vector RAG pipeline imported")
        
        # Note: This would require full pipeline setup with real LLM clients
        # For now, we'll just test the initialization
        
        pipeline = VectorRAGPowerPlantPipeline(enable_vector_rag=True)
        print("✅ Vector RAG pipeline initialized")
        
        # Test RAG stats
        rag_stats = pipeline.get_rag_stats()
        print(f"📊 RAG Stats: {rag_stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Vector RAG pipeline test failed: {e}")
        logging.error(f"Vector RAG pipeline test failed: {e}", exc_info=True)
        return False


async def main():
    """Run all LLM integration tests."""
    setup_logging()
    
    print("🚀 Vector RAG LLM Integration Test")
    print("=" * 60)
    
    # Test 1: RAG Extractor with Mock LLM
    rag_success = await test_rag_extractor_with_mock_llm()
    
    # Test 2: Pipeline Integration
    if rag_success:
        pipeline_success = await test_pipeline_integration()
    else:
        pipeline_success = False
    
    # Test 3: Complete Vector RAG Pipeline
    if pipeline_success:
        await test_vector_rag_pipeline()
    
    print("\n" + "=" * 60)
    print("🏁 LLM Integration tests completed!")
    print("Check 'test_vector_rag_llm.log' for detailed logs")


if __name__ == "__main__":
    asyncio.run(main())
