<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7ED321;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5BA517;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="orangeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F5A623;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D1890B;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="purpleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9013FE;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6200EA;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="800" fill="#f8f9fa"/>
  
  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">
    🧠 Vector RAG Enhanced Power Plant Extraction Pipeline
  </text>
  
  <!-- Input Stage -->
  <rect x="50" y="70" width="150" height="60" rx="10" fill="url(#blueGradient)" filter="url(#shadow)"/>
  <text x="125" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">Plant Name Input</text>
  <text x="125" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">"Jhajjar Power Plant"</text>
  
  <!-- Decision: Existing Content -->
  <polygon points="250,70 350,100 250,130 150,100" fill="url(#orangeGradient)" filter="url(#shadow)"/>
  <text x="250" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">Existing</text>
  <text x="250" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Content?</text>
  
  <!-- Scraping Path (if no existing content) -->
  <rect x="380" y="70" width="120" height="60" rx="10" fill="url(#blueGradient)" filter="url(#shadow)"/>
  <text x="440" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Web Scraping</text>
  <text x="440" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Create targeted_content</text>
  
  <!-- Vector Database Creation -->
  <rect x="50" y="180" width="200" height="80" rx="10" fill="url(#purpleGradient)" filter="url(#shadow)"/>
  <text x="150" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">🔨 Vector DB Creation</text>
  <text x="150" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">• Field-specific chunking</text>
  <text x="150" y="235" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">• Embedding generation</text>
  <text x="150" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">• 7 vector stores created</text>
  
  <!-- Field Processing -->
  <rect x="300" y="180" width="150" height="80" rx="10" fill="url(#greenGradient)" filter="url(#shadow)"/>
  <text x="375" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">🎯 Field Processing</text>
  <text x="375" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">lat, long, plant_type</text>
  <text x="375" y="235" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">units_id, plant_address</text>
  <text x="375" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">+ complex fields</text>
  
  <!-- Simple Fields Path -->
  <rect x="500" y="150" width="140" height="50" rx="8" fill="url(#greenGradient)" filter="url(#shadow)"/>
  <text x="570" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Simple Fields</text>
  <text x="570" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Direct RAG extraction</text>
  
  <!-- Complex Fields Path -->
  <rect x="500" y="220" width="140" height="50" rx="8" fill="url(#orangeGradient)" filter="url(#shadow)"/>
  <text x="570" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Complex Fields</text>
  <text x="570" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Granular extraction</text>
  
  <!-- Granular Extraction Detail -->
  <rect x="680" y="180" width="200" height="120" rx="10" fill="url(#orangeGradient)" filter="url(#shadow)"/>
  <text x="780" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">🔍 Granular Extraction</text>
  <text x="780" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">Grid Connectivity:</text>
  <text x="780" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">substation_names, voltage_levels</text>
  <text x="780" y="252" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">grid_operator, distances</text>
  <text x="780" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">PPA Details:</text>
  <text x="780" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">ppa_capacity, counterparty</text>
  <text x="780" y="297" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">haryana_distribution</text>
  
  <!-- Vector RAG Process -->
  <rect x="920" y="180" width="160" height="120" rx="10" fill="url(#purpleGradient)" filter="url(#shadow)"/>
  <text x="1000" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">🧠 Vector RAG</text>
  <text x="1000" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">1. Semantic search</text>
  <text x="1000" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">2. Retrieve top chunks</text>
  <text x="1000" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">3. LLM extraction</text>
  <text x="1000" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">4. Structure building</text>
  <text x="1000" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">OpenAI GPT-4o-mini</text>
  
  <!-- Structure Building -->
  <rect x="300" y="350" width="200" height="80" rx="10" fill="url(#greenGradient)" filter="url(#shadow)"/>
  <text x="400" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">🏗️ Structure Building</text>
  <text x="400" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">• Combine granular data</text>
  <text x="400" y="405" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">• Calculate percentages</text>
  <text x="400" y="420" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">• Build JSON structures</text>
  
  <!-- Intelligent Fallbacks -->
  <rect x="550" y="350" width="180" height="80" rx="10" fill="url(#orangeGradient)" filter="url(#shadow)"/>
  <text x="640" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">🔄 Smart Fallbacks</text>
  <text x="640" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">• plant_address from lat/long</text>
  <text x="640" y="405" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">• total_capacity from units</text>
  <text x="640" y="420" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">• Enhanced prompts</text>
  
  <!-- Final Output -->
  <rect x="400" y="480" width="400" height="120" rx="10" fill="url(#greenGradient)" filter="url(#shadow)"/>
  <text x="600" y="505" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">🎉 Final Output - 100% Success</text>
  <text x="450" y="525" text-anchor="start" font-family="Arial, sans-serif" font-size="10" fill="white">✅ lat: 28.6063</text>
  <text x="450" y="540" text-anchor="start" font-family="Arial, sans-serif" font-size="10" fill="white">✅ long: 76.6565</text>
  <text x="450" y="555" text-anchor="start" font-family="Arial, sans-serif" font-size="10" fill="white">✅ plant_address: Jhajjar, Haryana, India</text>
  <text x="450" y="570" text-anchor="start" font-family="Arial, sans-serif" font-size="10" fill="white">✅ plant_type: coal</text>
  <text x="450" y="585" text-anchor="start" font-family="Arial, sans-serif" font-size="10" fill="white">✅ units_id: [1, 2]</text>
  
  <text x="650" y="525" text-anchor="start" font-family="Arial, sans-serif" font-size="10" fill="white">✅ grid_connectivity_maps:</text>
  <text x="670" y="540" text-anchor="start" font-family="Arial, sans-serif" font-size="9" fill="white">Kabulpur, Dipalpur substations</text>
  <text x="670" y="555" text-anchor="start" font-family="Arial, sans-serif" font-size="9" fill="white">400kV, 220kV, HVPNL operator</text>
  <text x="650" y="570" text-anchor="start" font-family="Arial, sans-serif" font-size="10" fill="white">✅ ppa_details:</text>
  <text x="670" y="585" text-anchor="start" font-family="Arial, sans-serif" font-size="9" fill="white">132MW, 25 years, Haryana utilities</text>
  
  <!-- Performance Metrics -->
  <rect x="50" y="650" width="300" height="80" rx="10" fill="url(#blueGradient)" filter="url(#shadow)"/>
  <text x="200" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">📊 Performance Metrics</text>
  <text x="200" y="690" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">⚡ Execution Time: 31.60 seconds</text>
  <text x="200" y="705" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">🎯 Success Rate: 100% (7/7 fields)</text>
  <text x="200" y="720" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">🧠 Vector DB: 389 chunks across 7 fields</text>
  
  <!-- Technology Stack -->
  <rect x="400" y="650" width="300" height="80" rx="10" fill="url(#purpleGradient)" filter="url(#shadow)"/>
  <text x="550" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">🛠️ Technology Stack</text>
  <text x="550" y="690" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">🤖 OpenAI GPT-4o-mini</text>
  <text x="550" y="705" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">🔍 all-MiniLM-L6-v2 embeddings</text>
  <text x="550" y="720" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">📊 FAISS vector similarity search</text>
  
  <!-- Key Innovation -->
  <rect x="750" y="650" width="300" height="80" rx="10" fill="url(#orangeGradient)" filter="url(#shadow)"/>
  <text x="900" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">💡 Key Innovation</text>
  <text x="900" y="690" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">🔍 Granular Component Searches</text>
  <text x="900" y="705" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">🏗️ Intelligent Structure Building</text>
  <text x="900" y="720" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">🔄 Smart Fallback Mechanisms</text>
  
  <!-- Arrows -->
  <!-- Input to Decision -->
  <path d="M 200 100 L 230 100" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Decision to Scraping -->
  <path d="M 300 85 L 380 85" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <text x="340" y="80" font-family="Arial, sans-serif" font-size="9" fill="#2c3e50">No</text>
  
  <!-- Decision to Vector DB -->
  <path d="M 250 130 L 150 180" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <text x="180" y="160" font-family="Arial, sans-serif" font-size="9" fill="#2c3e50">Yes</text>
  
  <!-- Scraping to Vector DB -->
  <path d="M 440 130 L 150 180" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Vector DB to Field Processing -->
  <path d="M 250 220 L 300 220" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Field Processing to Simple/Complex -->
  <path d="M 450 200 L 500 175" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 450 240 L 500 245" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Complex to Granular -->
  <path d="M 640 245 L 680 240" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Granular to Vector RAG -->
  <path d="M 880 240 L 920 240" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Simple to Vector RAG -->
  <path d="M 640 175 L 920 200" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Vector RAG to Structure Building -->
  <path d="M 1000 300 L 400 350" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Structure Building to Fallbacks -->
  <path d="M 500 390 L 550 390" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Fallbacks to Final Output -->
  <path d="M 640 430 L 600 480" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Arrow marker definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
    </marker>
  </defs>
</svg>
