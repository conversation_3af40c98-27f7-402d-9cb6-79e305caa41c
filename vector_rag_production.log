2025-05-29 00:04:23,844 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with Groq
2025-05-29 00:04:23,861 - src.plant_details_extractor - INFO - Plant details extractor initialized with Groq
2025-05-29 00:04:23,861 - src.vector_rag.pipeline_integration - INFO - ✅ Vector RAG integration enabled
2025-05-29 00:04:23,861 - src.vector_rag_pipeline - INFO - Vector RAG Enhanced Pipeline initialized (RAG enabled: True)
2025-05-29 00:04:23,861 - src.vector_rag_pipeline - INFO - 🚀 Starting Vector RAG enhanced extraction for: Jhajjar Power Plant
2025-05-29 00:04:23,861 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-29 00:04:23,861 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-29 00:04:58,714 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-29 00:04:58,714 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-29 00:05:00,553 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-29 00:05:01,554 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:05:02,631 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:05:03,632 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-29 00:05:08,251 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-29 00:05:09,253 - src.simple_pipeline - INFO - Scraping 4/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-29 00:05:11,175 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-29 00:05:12,176 - src.simple_pipeline - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-29 00:05:14,095 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-29 00:05:15,097 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-29 00:05:15,097 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-29 00:05:15,097 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['wikipedia', 'other', 'company_official']}
2025-05-29 00:05:15,098 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-29 00:05:15,098 - src.groq_client - INFO - Starting LLM extraction for 5 content pieces
2025-05-29 00:05:15,698 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:05:15,938 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:05:15,941 - src.groq_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.90)
2025-05-29 00:05:16,654 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:05:16,657 - src.groq_client - INFO - Extracted country_name: India (confidence: 0.80)
2025-05-29 00:05:17,385 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:05:17,388 - src.groq_client - INFO - Extracted province: Haryana (confidence: 0.70)
2025-05-29 00:05:18,100 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:05:18,104 - src.groq_client - INFO - Extracted plants_count: 1 (confidence: 0.80)
2025-05-29 00:05:18,818 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:05:18,820 - src.groq_client - INFO - Extracted plant_types: ['coal'] (confidence: 0.80)
2025-05-29 00:05:19,982 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:05:19,985 - src.groq_client - INFO - Extracted ppa_flag: Plant (confidence: 0.80)
2025-05-29 00:05:20,717 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:05:20,720 - src.groq_client - INFO - Extracted currency_in: INR (confidence: 0.90)
2025-05-29 00:05:21,449 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:05:21,452 - src.groq_client - INFO - Extracted financial_year: 04-03 (confidence: 0.90)
2025-05-29 00:05:21,953 - src.groq_client - INFO - LLM extraction completed
2025-05-29 00:05:21,954 - src.simple_pipeline - INFO - ✅ Organizational extraction completed
2025-05-29 00:05:21,955 - src.simple_pipeline - INFO - 🔧 Step 4: Extracting plant details with smart field-by-field approach
2025-05-29 00:05:21,955 - src.simple_pipeline - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-29 00:05:21,955 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-29 00:05:22,376 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:05:22,379 - src.plant_details_extractor - INFO - Extracted name: Mahatma Gandhi Super Thermal Power Project (confidence: 0.80)
2025-05-29 00:05:22,957 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:05:22,958 - groq._base_client - INFO - Retrying request to /openai/v1/chat/completions in 5.000000 seconds
2025-05-29 00:05:28,254 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:05:28,256 - src.plant_details_extractor - INFO - Extracted plant_type: coal (confidence: 0.80)
2025-05-29 00:05:28,816 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:05:28,817 - groq._base_client - INFO - Retrying request to /openai/v1/chat/completions in 7.000000 seconds
2025-05-29 00:05:36,155 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:05:36,158 - src.plant_details_extractor - INFO - Extracted plant_address: Jhajjar, Haryana, India (confidence: 0.80)
2025-05-29 00:05:36,724 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:05:36,726 - groq._base_client - INFO - Retrying request to /openai/v1/chat/completions in 8.000000 seconds
2025-05-29 00:05:45,025 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:05:45,028 - src.plant_details_extractor - INFO - Extracted lat:  (confidence: 0.00)
2025-05-29 00:05:45,609 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:05:45,610 - groq._base_client - INFO - Retrying request to /openai/v1/chat/completions in 8.000000 seconds
2025-05-29 00:05:53,941 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:05:53,944 - src.plant_details_extractor - INFO - Extracted long:  (confidence: 0.00)
2025-05-29 00:05:54,513 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:05:54,515 - groq._base_client - INFO - Retrying request to /openai/v1/chat/completions in 7.000000 seconds
2025-05-29 00:06:01,977 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:06:01,981 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: 5 validation errors for ExtractionResult
extracted_value.str
  Input should be a valid string [type=string_type, input_value=[1, 2], input_type=list]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
extracted_value.int
  Input should be a valid integer [type=int_type, input_value=[1, 2], input_type=list]
    For further information visit https://errors.pydantic.dev/2.5/v/int_type
extracted_value.bool
  Input should be a valid boolean [type=bool_type, input_value=[1, 2], input_type=list]
    For further information visit https://errors.pydantic.dev/2.5/v/bool_type
extracted_value.list[str].0
  Input should be a valid string [type=string_type, input_value=1, input_type=int]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
extracted_value.list[str].1
  Input should be a valid string [type=string_type, input_value=2, input_type=int]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
2025-05-29 00:06:01,981 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-29 00:06:02,482 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-29 00:06:02,483 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "capacity"'
2025-05-29 00:06:02,483 - src.plant_details_extractor - INFO - Using organization name for plant name: CLP India Private Limited
2025-05-29 00:06:02,483 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-29 00:06:02,483 - src.simple_pipeline - INFO - ✅ Field 'name' extracted from cache
2025-05-29 00:06:02,484 - src.simple_pipeline - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-29 00:06:02,484 - src.simple_pipeline - INFO - ✅ Field 'plant_address' extracted from cache
2025-05-29 00:06:02,484 - src.simple_pipeline - INFO - ✅ Field 'plant_id' extracted from cache
2025-05-29 00:06:02,485 - src.simple_pipeline - INFO - 🔍 Field 'lat' is missing - will search specifically
2025-05-29 00:06:02,485 - src.simple_pipeline - INFO - 🔍 Field 'long' is missing - will search specifically
2025-05-29 00:06:02,485 - src.simple_pipeline - INFO - 🔍 Field 'grid_connectivity_maps' is missing - will search specifically
2025-05-29 00:06:02,485 - src.simple_pipeline - INFO - 🔍 Field 'ppa_details' is missing - will search specifically
2025-05-29 00:06:02,485 - src.simple_pipeline - INFO - 🔍 Field 'units_id' is missing - will search specifically
2025-05-29 00:06:02,485 - src.simple_pipeline - INFO - 🎯 Starting targeted searches for 5 missing fields
2025-05-29 00:06:02,485 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'lat' data
2025-05-29 00:06:02,486 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant latitude coordinates GPS location'
2025-05-29 00:06:06,335 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:06:08,004 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:06:09,006 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-29 00:06:09,935 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-29 00:06:10,936 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-29 00:06:13,430 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-29 00:06:14,432 - src.simple_pipeline - INFO - ✅ Found targeted content for 'lat' - 3 sources
2025-05-29 00:06:14,434 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'lat' using RAG: 28.607111
2025-05-29 00:06:14,435 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'long' data
2025-05-29 00:06:14,435 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant longitude coordinates GPS location'
2025-05-29 00:06:51,095 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:06:56,061 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:06:57,062 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-29 00:07:03,624 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-29 00:07:04,626 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-29 00:07:05,789 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-29 00:07:06,791 - src.simple_pipeline - INFO - ✅ Found targeted content for 'long' - 3 sources
2025-05-29 00:07:06,793 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'long' using RAG: 76.6565
2025-05-29 00:07:06,794 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-29 00:07:06,794 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant grid connection substation transmission line'
2025-05-29 00:07:10,245 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-29 00:07:16,900 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-29 00:07:16,902 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-29 00:07:16,902 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-29 00:07:17,903 - src.simple_pipeline - INFO - Scraping 2/3: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-29 00:07:42,620 - src.pdf_processor - INFO - Successfully extracted 112384 chars from PDF using pdfplumber: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-29 00:07:42,623 - src.scraper_client - INFO - Successfully processed PDF: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars, score: 1.00)
2025-05-29 00:07:42,623 - src.simple_pipeline - INFO - Successfully scraped http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars)
2025-05-29 00:07:43,624 - src.simple_pipeline - INFO - Scraping 3/3: https://www.indigrid.co.in/portfolio-assets/jhajjar-kt-transco-pvt-ltd-jktpl/
2025-05-29 00:07:52,108 - src.simple_pipeline - INFO - Successfully scraped https://www.indigrid.co.in/portfolio-assets/jhajjar-kt-transco-pvt-ltd-jktpl/ (846 chars)
2025-05-29 00:07:53,110 - src.simple_pipeline - INFO - ✅ Found targeted content for 'grid_connectivity_maps' - 3 sources
2025-05-29 00:07:53,112 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'grid_connectivity_maps' using RAG: [{'description': 'Grid connectivity details for Jhajjar Power Plant', 'details': [{'substation_name': 'Sonipat Substation', 'substation_type': 'transmission', 'capacity': '400 kV', 'latitude': '', 'longitude': '', 'description': 'Primary transmission connection point - approximately 70 km northeast', 'projects': [{'description': '400 kV transmission line from Jhajjar to Sonipat', 'distance': '70 km'}]}, {'substation_name': 'Mahendragarh Substation', 'substation_type': 'transmission', 'capacity': '400 kV', 'latitude': '', 'longitude': '', 'description': 'Secondary transmission connection point - approximately 50 km southwest', 'projects': [{'description': '400 kV transmission line from Jhajjar to Mahendragarh', 'distance': '50 km'}]}]}]
2025-05-29 00:07:53,112 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'ppa_details' data
2025-05-29 00:07:53,112 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant power purchase agreement PPA contract details'
2025-05-29 00:07:56,574 - src.simple_pipeline - INFO - Scraping 1/3: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-29 00:08:02,649 - src.pdf_processor - INFO - Successfully extracted 36539 chars from PDF using pdfplumber: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-29 00:08:02,651 - src.scraper_client - INFO - Successfully processed PDF: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars, score: 1.00)
2025-05-29 00:08:02,651 - src.simple_pipeline - INFO - Successfully scraped https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars)
2025-05-29 00:08:03,652 - src.simple_pipeline - INFO - Scraping 2/3: https://cercind.gov.in/2022/orders/363-MP-2019.pdf
2025-05-29 00:08:09,034 - src.pdf_processor - INFO - Successfully extracted 17990 chars from PDF using pdfplumber: https://cercind.gov.in/2022/orders/363-MP-2019.pdf
2025-05-29 00:08:09,035 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2022/orders/363-MP-2019.pdf (17981 chars, score: 1.00)
2025-05-29 00:08:09,035 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2022/orders/363-MP-2019.pdf (17981 chars)
2025-05-29 00:08:10,036 - src.simple_pipeline - INFO - Scraping 3/3: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-29 00:08:22,377 - src.pdf_processor - INFO - Successfully extracted 111606 chars from PDF using pdfplumber: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-29 00:08:22,379 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars, score: 1.00)
2025-05-29 00:08:22,379 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars)
2025-05-29 00:08:23,381 - src.simple_pipeline - INFO - ✅ Found targeted content for 'ppa_details' - 3 sources
2025-05-29 00:08:23,384 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'ppa_details' using RAG: [{'description': 'Power Purchase Agreement for Jhajjar Power Plant', 'capacity': '1320 MW', 'capacity_unit': 'MW', 'start_date': '2012', 'end_date': '', 'tenure': 30, 'tenure_type': 'Years', 'respondents': [{'name': 'Haryana State Distribution Companies', 'capacity': '1188 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}, {'name': 'External Power Buyers', 'capacity': '132 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}]}]
2025-05-29 00:08:23,384 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'units_id' data
2025-05-29 00:08:23,385 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant units generators unit numbers'
2025-05-29 00:08:26,848 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-29 00:08:34,360 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-29 00:08:34,362 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-29 00:08:34,362 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-29 00:08:35,363 - src.simple_pipeline - INFO - Scraping 2/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:08:37,760 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:08:38,761 - src.simple_pipeline - INFO - Scraping 3/3: https://carbonmarketwatch.org/wp-content/uploads/2012/10/comments_on_jhajjar_haryana_project.pdf
2025-05-29 00:08:44,471 - src.pdf_processor - INFO - Successfully extracted 67258 chars from PDF using pdfplumber: https://carbonmarketwatch.org/wp-content/uploads/2012/10/comments_on_jhajjar_haryana_project.pdf
2025-05-29 00:08:44,473 - src.scraper_client - INFO - Successfully processed PDF: https://carbonmarketwatch.org/wp-content/uploads/2012/10/comments_on_jhajjar_haryana_project.pdf (50003 chars, score: 1.00)
2025-05-29 00:08:44,473 - src.simple_pipeline - INFO - Successfully scraped https://carbonmarketwatch.org/wp-content/uploads/2012/10/comments_on_jhajjar_haryana_project.pdf (50003 chars)
2025-05-29 00:08:45,475 - src.simple_pipeline - INFO - ✅ Found targeted content for 'units_id' - 3 sources
2025-05-29 00:08:45,484 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'units_id' using RAG: ['Unit 1', 'Unit 3', 'Unit 5', 'Unit 2', 'Unit 6', 'Unit 4', 'Unit 250']
2025-05-29 00:08:45,494 - src.simple_pipeline - INFO - 💾 Targeted content saved to jhajjar_power_plant_targeted_content_20250529_000845.json
2025-05-29 00:08:45,494 - src.simple_pipeline - ERROR - Error creating PlantDetails object: 7 validation errors for PlantDetails
units_id.0
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
units_id.1
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 3', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
units_id.2
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 5', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
units_id.3
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 2', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
units_id.4
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 6', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
units_id.5
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 4', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
units_id.6
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 250', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
2025-05-29 00:08:45,494 - src.simple_pipeline - INFO - 🎉 Simplified extraction completed for: Jhajjar Power Plant
2025-05-29 00:08:45,494 - src.simple_pipeline - INFO - ⏱️  Total time: 261.6s
2025-05-29 00:08:45,494 - src.simple_pipeline - INFO - 💾 Cache efficiency: 4 fields from cache, 5 targeted searches
2025-05-29 00:08:45,494 - src.vector_rag_pipeline - INFO - 🧠 Starting Vector RAG enhancement phase
2025-05-29 00:08:45,494 - src.vector_rag_pipeline - INFO - 📁 Using targeted content file: jhajjar_power_plant_targeted_content_20250529_000845.json
2025-05-29 00:08:45,494 - src.vector_rag_pipeline - ERROR - Error in Vector RAG enhancement: 'dict' object has no attribute 'model_dump'
2025-05-29 00:08:45,495 - src.vector_rag_pipeline - INFO - ✅ Vector RAG enhanced extraction completed in 261.63s
2025-05-29 00:08:45,495 - root - ERROR - Production test failed: 'dict' object has no attribute 'model_dump'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/clem_transition/run_vector_rag_production.py", line 87, in test_with_existing_targeted_content
    plant_data = plant_details.model_dump()
AttributeError: 'dict' object has no attribute 'model_dump'
2025-05-29 00:08:45,506 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with Groq
2025-05-29 00:08:45,528 - src.plant_details_extractor - INFO - Plant details extractor initialized with Groq
2025-05-29 00:08:45,528 - src.vector_rag.pipeline_integration - INFO - ✅ Vector RAG integration enabled
2025-05-29 00:08:45,528 - src.vector_rag_pipeline - INFO - Vector RAG Enhanced Pipeline initialized (RAG enabled: True)
2025-05-29 00:08:45,528 - src.vector_rag_pipeline - INFO - 🚀 Starting Vector RAG enhanced extraction for: Jhajjar Power Plant
2025-05-29 00:08:45,528 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-29 00:08:45,528 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-29 00:08:51,541 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-29 00:08:51,542 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-29 00:08:55,878 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-29 00:08:56,880 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:08:58,676 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:08:59,677 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-29 00:09:03,332 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-29 00:09:04,333 - src.simple_pipeline - INFO - Scraping 4/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-29 00:09:10,074 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-29 00:09:11,076 - src.simple_pipeline - INFO - Scraping 5/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-29 00:09:14,327 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-29 00:09:15,329 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-29 00:09:15,329 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-29 00:09:15,329 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['wikipedia', 'other', 'company_official']}
2025-05-29 00:09:15,330 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-29 00:09:15,330 - src.groq_client - INFO - Starting LLM extraction for 5 content pieces
2025-05-29 00:09:15,754 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:09:16,035 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:09:16,037 - src.groq_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.90)
2025-05-29 00:09:16,778 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:09:16,780 - src.groq_client - INFO - Extracted country_name: India (confidence: 0.80)
2025-05-29 00:09:17,494 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:09:17,496 - src.groq_client - INFO - Extracted province: Haryana (confidence: 0.70)
2025-05-29 00:09:18,209 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:09:18,211 - src.groq_client - INFO - Extracted plants_count: 1 (confidence: 0.80)
2025-05-29 00:09:18,919 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:09:18,922 - src.groq_client - INFO - Extracted plant_types: ['coal'] (confidence: 0.80)
2025-05-29 00:09:19,645 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:09:19,647 - src.groq_client - INFO - Extracted ppa_flag: None (confidence: 0.10)
2025-05-29 00:09:20,397 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:09:20,399 - src.groq_client - INFO - Extracted currency_in: None (confidence: 0.10)
2025-05-29 00:09:21,133 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:09:21,134 - src.groq_client - INFO - Extracted financial_year: 04-03 (confidence: 0.90)
2025-05-29 00:09:21,636 - src.groq_client - INFO - LLM extraction completed
2025-05-29 00:09:21,637 - src.simple_pipeline - INFO - ✅ Organizational extraction completed
2025-05-29 00:09:21,637 - src.simple_pipeline - INFO - 🔧 Step 4: Extracting plant details with smart field-by-field approach
2025-05-29 00:09:21,637 - src.simple_pipeline - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-29 00:09:21,637 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-29 00:09:21,978 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:09:21,980 - src.plant_details_extractor - INFO - Extracted name: Mahatma Gandhi Super Thermal Power Project (confidence: 0.80)
2025-05-29 00:09:22,583 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:09:22,585 - groq._base_client - INFO - Retrying request to /openai/v1/chat/completions in 2.000000 seconds
2025-05-29 00:09:24,886 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:09:24,889 - src.plant_details_extractor - INFO - Extracted plant_type: coal (confidence: 0.80)
2025-05-29 00:09:25,452 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:09:25,452 - groq._base_client - INFO - Retrying request to /openai/v1/chat/completions in 8.000000 seconds
2025-05-29 00:09:33,919 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:09:33,920 - src.plant_details_extractor - INFO - Extracted plant_address: Jhajjar, Haryana, India (confidence: 0.80)
2025-05-29 00:09:34,486 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:09:34,486 - groq._base_client - INFO - Retrying request to /openai/v1/chat/completions in 8.000000 seconds
2025-05-29 00:09:42,888 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:09:42,890 - src.plant_details_extractor - INFO - Extracted lat: 28.6062 (confidence: 0.90)
2025-05-29 00:09:43,586 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:09:43,587 - groq._base_client - INFO - Retrying request to /openai/v1/chat/completions in 7.000000 seconds
2025-05-29 00:09:50,882 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:09:50,885 - src.plant_details_extractor - INFO - Extracted long:  (confidence: 0.00)
2025-05-29 00:09:51,470 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:09:51,471 - groq._base_client - INFO - Retrying request to /openai/v1/chat/completions in 8.000000 seconds
2025-05-29 00:09:59,789 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:09:59,790 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: 5 validation errors for ExtractionResult
extracted_value.str
  Input should be a valid string [type=string_type, input_value=[1, 2], input_type=list]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
extracted_value.int
  Input should be a valid integer [type=int_type, input_value=[1, 2], input_type=list]
    For further information visit https://errors.pydantic.dev/2.5/v/int_type
extracted_value.bool
  Input should be a valid boolean [type=bool_type, input_value=[1, 2], input_type=list]
    For further information visit https://errors.pydantic.dev/2.5/v/bool_type
extracted_value.list[str].0
  Input should be a valid string [type=string_type, input_value=1, input_type=int]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
extracted_value.list[str].1
  Input should be a valid string [type=string_type, input_value=2, input_type=int]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
2025-05-29 00:09:59,790 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-29 00:10:00,292 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-29 00:10:00,292 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "capacity"'
2025-05-29 00:10:00,293 - src.plant_details_extractor - INFO - Using organization name for plant name: CLP India Private Limited
2025-05-29 00:10:00,293 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-29 00:10:00,293 - src.simple_pipeline - INFO - ✅ Field 'name' extracted from cache
2025-05-29 00:10:00,293 - src.simple_pipeline - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-29 00:10:00,293 - src.simple_pipeline - INFO - ✅ Field 'plant_address' extracted from cache
2025-05-29 00:10:00,293 - src.simple_pipeline - INFO - ✅ Field 'lat' extracted from cache
2025-05-29 00:10:00,294 - src.simple_pipeline - INFO - ✅ Field 'plant_id' extracted from cache
2025-05-29 00:10:00,294 - src.simple_pipeline - INFO - 🔍 Field 'long' is missing - will search specifically
2025-05-29 00:10:00,294 - src.simple_pipeline - INFO - 🔍 Field 'grid_connectivity_maps' is missing - will search specifically
2025-05-29 00:10:00,294 - src.simple_pipeline - INFO - 🔍 Field 'ppa_details' is missing - will search specifically
2025-05-29 00:10:00,294 - src.simple_pipeline - INFO - 🔍 Field 'units_id' is missing - will search specifically
2025-05-29 00:10:00,294 - src.simple_pipeline - INFO - 🎯 Starting targeted searches for 4 missing fields
2025-05-29 00:10:00,294 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'long' data
2025-05-29 00:10:00,294 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant longitude coordinates GPS location'
2025-05-29 00:10:05,744 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:10:10,758 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:10:11,760 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-29 00:10:13,629 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-29 00:10:14,631 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-29 00:10:15,688 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-29 00:10:16,690 - src.simple_pipeline - INFO - ✅ Found targeted content for 'long' - 3 sources
2025-05-29 00:10:16,690 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'long' using RAG: 76.6565
2025-05-29 00:10:16,690 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-29 00:10:16,690 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant grid connection substation transmission line'
2025-05-29 00:11:13,042 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-29 00:11:19,287 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-29 00:11:19,288 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-29 00:11:19,288 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-29 00:11:20,290 - src.simple_pipeline - INFO - Scraping 2/3: https://powerline.net.in/2024/10/24/powergrid-secures-bids-for-two-ists-projects-on-boot-basis/
2025-05-29 00:11:21,621 - src.simple_pipeline - INFO - Successfully scraped https://powerline.net.in/2024/10/24/powergrid-secures-bids-for-two-ists-projects-on-boot-basis/ (1192 chars)
2025-05-29 00:11:22,622 - src.simple_pipeline - INFO - Scraping 3/3: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-29 00:11:42,114 - src.pdf_processor - INFO - Successfully extracted 112384 chars from PDF using pdfplumber: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-29 00:11:42,117 - src.scraper_client - INFO - Successfully processed PDF: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars, score: 1.00)
2025-05-29 00:11:42,117 - src.simple_pipeline - INFO - Successfully scraped http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars)
2025-05-29 00:11:43,119 - src.simple_pipeline - INFO - ✅ Found targeted content for 'grid_connectivity_maps' - 3 sources
2025-05-29 00:11:43,121 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'grid_connectivity_maps' using RAG: [{'description': 'Grid connectivity details for Jhajjar Power Plant', 'details': [{'substation_name': 'Sonipat Substation', 'substation_type': 'transmission', 'capacity': '400 kV', 'latitude': '', 'longitude': '', 'description': 'Primary transmission connection point - approximately 70 km northeast', 'projects': [{'description': '400 kV transmission line from Jhajjar to Sonipat', 'distance': '70 km'}]}, {'substation_name': 'Mahendragarh Substation', 'substation_type': 'transmission', 'capacity': '400 kV', 'latitude': '', 'longitude': '', 'description': 'Secondary transmission connection point - approximately 50 km southwest', 'projects': [{'description': '400 kV transmission line from Jhajjar to Mahendragarh', 'distance': '50 km'}]}]}]
2025-05-29 00:11:43,121 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'ppa_details' data
2025-05-29 00:11:43,121 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant power purchase agreement PPA contract details'
2025-05-29 00:11:50,909 - src.simple_pipeline - INFO - Scraping 1/3: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-29 00:12:00,208 - src.pdf_processor - INFO - Successfully extracted 36539 chars from PDF using pdfplumber: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-29 00:12:00,210 - src.scraper_client - INFO - Successfully processed PDF: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars, score: 1.00)
2025-05-29 00:12:00,210 - src.simple_pipeline - INFO - Successfully scraped https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars)
2025-05-29 00:12:01,211 - src.simple_pipeline - INFO - Scraping 2/3: https://cercind.gov.in/2022/orders/363-MP-2019.pdf
2025-05-29 00:12:05,568 - src.pdf_processor - INFO - Successfully extracted 17990 chars from PDF using pdfplumber: https://cercind.gov.in/2022/orders/363-MP-2019.pdf
2025-05-29 00:12:05,568 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2022/orders/363-MP-2019.pdf (17981 chars, score: 1.00)
2025-05-29 00:12:05,568 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2022/orders/363-MP-2019.pdf (17981 chars)
2025-05-29 00:12:06,570 - src.simple_pipeline - INFO - Scraping 3/3: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-29 00:12:22,489 - src.pdf_processor - INFO - Successfully extracted 111606 chars from PDF using pdfplumber: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-29 00:12:22,490 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars, score: 1.00)
2025-05-29 00:12:22,490 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars)
2025-05-29 00:12:23,492 - src.simple_pipeline - INFO - ✅ Found targeted content for 'ppa_details' - 3 sources
2025-05-29 00:12:23,494 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'ppa_details' using RAG: [{'description': 'Power Purchase Agreement for Jhajjar Power Plant', 'capacity': '1320 MW', 'capacity_unit': 'MW', 'start_date': '2012', 'end_date': '', 'tenure': 30, 'tenure_type': 'Years', 'respondents': [{'name': 'Haryana State Distribution Companies', 'capacity': '1188 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}, {'name': 'External Power Buyers', 'capacity': '132 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}]}]
2025-05-29 00:12:23,495 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'units_id' data
2025-05-29 00:12:23,495 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant units generators unit numbers'
2025-05-29 00:12:37,785 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-29 00:12:53,852 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-29 00:12:53,854 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-29 00:12:53,854 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-29 00:12:54,856 - src.simple_pipeline - INFO - Scraping 2/3: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf
2025-05-29 00:13:04,741 - src.pdf_processor - INFO - Successfully extracted 89455 chars from PDF using pdfplumber: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf
2025-05-29 00:13:04,743 - src.scraper_client - INFO - Successfully processed PDF: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf (50003 chars, score: 1.00)
2025-05-29 00:13:04,743 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf (50003 chars)
2025-05-29 00:13:05,744 - src.simple_pipeline - INFO - Scraping 3/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:13:07,067 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:13:08,070 - src.simple_pipeline - INFO - ✅ Found targeted content for 'units_id' - 3 sources
2025-05-29 00:13:08,079 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'units_id' using RAG: ['Unit 1', 'Unit 3', 'Unit 250']
2025-05-29 00:13:08,085 - src.simple_pipeline - INFO - 💾 Targeted content saved to jhajjar_power_plant_targeted_content_20250529_001308.json
2025-05-29 00:13:08,085 - src.simple_pipeline - ERROR - Error creating PlantDetails object: 3 validation errors for PlantDetails
units_id.0
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
units_id.1
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 3', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
units_id.2
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 250', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
2025-05-29 00:13:08,085 - src.simple_pipeline - INFO - 🎉 Simplified extraction completed for: Jhajjar Power Plant
2025-05-29 00:13:08,085 - src.simple_pipeline - INFO - ⏱️  Total time: 262.6s
2025-05-29 00:13:08,085 - src.simple_pipeline - INFO - 💾 Cache efficiency: 5 fields from cache, 4 targeted searches
2025-05-29 00:13:08,085 - src.vector_rag_pipeline - INFO - 🧠 Starting Vector RAG enhancement phase
2025-05-29 00:13:08,086 - src.vector_rag_pipeline - INFO - 📁 Using targeted content file: jhajjar_power_plant_targeted_content_20250529_001308.json
2025-05-29 00:13:08,086 - src.vector_rag_pipeline - ERROR - Error in Vector RAG enhancement: 'dict' object has no attribute 'model_dump'
2025-05-29 00:13:08,086 - src.vector_rag_pipeline - INFO - ✅ Vector RAG enhanced extraction completed in 262.56s
2025-05-29 00:13:08,087 - src.simple_pipeline - INFO - 📊 Organizational results saved to full_pipeline_org_20250529_001308.json
2025-05-29 00:13:08,087 - src.simple_pipeline - ERROR - Error saving results: 'dict' object has no attribute 'model_dump'
2025-05-29 00:13:08,087 - src.vector_rag_pipeline - INFO - Vector RAG statistics saved to vector_rag_stats.json
2025-05-29 00:13:08,099 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with Groq
2025-05-29 00:13:08,121 - src.plant_details_extractor - INFO - Plant details extractor initialized with Groq
2025-05-29 00:13:08,121 - src.vector_rag_pipeline - INFO - Vector RAG Enhanced Pipeline initialized (RAG enabled: False)
2025-05-29 00:13:08,121 - src.vector_rag_pipeline - INFO - 🚀 Starting Vector RAG enhanced extraction for: Jhajjar Power Plant
2025-05-29 00:13:08,121 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-29 00:13:08,121 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-29 00:13:14,484 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-29 00:13:14,485 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-29 00:13:16,198 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-29 00:13:17,200 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:13:18,167 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:13:19,169 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-29 00:13:25,180 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-29 00:13:26,181 - src.simple_pipeline - INFO - Scraping 4/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-29 00:13:27,740 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-29 00:13:28,741 - src.simple_pipeline - INFO - Scraping 5/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-29 00:13:37,564 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-29 00:13:38,566 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-29 00:13:38,566 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-29 00:13:38,567 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['wikipedia', 'other', 'company_official']}
2025-05-29 00:13:38,567 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-29 00:13:38,568 - src.groq_client - INFO - Starting LLM extraction for 5 content pieces
2025-05-29 00:13:39,108 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:13:39,330 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 00:13:39,333 - src.groq_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.90)
2025-05-29 00:13:39,899 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:13:39,901 - src.groq_client - ERROR - Groq extraction failed for field country_name: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99639, Requested 1138. Please try again in 11m10.759s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:13:39,901 - src.groq_client - INFO - Extracted country_name: None (confidence: 0.00)
2025-05-29 00:13:40,472 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:13:40,474 - src.groq_client - ERROR - Groq extraction failed for field province: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99638, Requested 1156. Please try again in 11m25.738s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:13:40,475 - src.groq_client - INFO - Extracted province: None (confidence: 0.00)
2025-05-29 00:13:41,037 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:13:41,039 - src.groq_client - ERROR - Groq extraction failed for field plants_count: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99638, Requested 1137. Please try again in 11m8.752999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:13:41,039 - src.groq_client - INFO - Extracted plants_count: None (confidence: 0.00)
2025-05-29 00:13:41,648 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:13:41,649 - src.groq_client - ERROR - Groq extraction failed for field plant_types: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99637, Requested 1157. Please try again in 11m25.425s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:13:41,650 - src.groq_client - INFO - Extracted plant_types: None (confidence: 0.00)
2025-05-29 00:13:42,214 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:13:42,216 - src.groq_client - ERROR - Groq extraction failed for field ppa_flag: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99636, Requested 1210. Please try again in 12m10.65s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:13:42,216 - src.groq_client - INFO - Extracted ppa_flag: None (confidence: 0.00)
2025-05-29 00:13:42,782 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:13:42,784 - src.groq_client - ERROR - Groq extraction failed for field currency_in: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99636, Requested 1163. Please try again in 11m29.474999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:13:42,785 - src.groq_client - INFO - Extracted currency_in: None (confidence: 0.00)
2025-05-29 00:13:43,351 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:13:43,353 - src.groq_client - ERROR - Groq extraction failed for field financial_year: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99635, Requested 1283. Please try again in 13m12.584s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:13:43,353 - src.groq_client - INFO - Extracted financial_year: None (confidence: 0.00)
2025-05-29 00:13:43,855 - src.groq_client - INFO - LLM extraction completed
2025-05-29 00:13:43,855 - src.simple_pipeline - INFO - ✅ Organizational extraction completed
2025-05-29 00:13:43,856 - src.simple_pipeline - INFO - 🔧 Step 4: Extracting plant details with smart field-by-field approach
2025-05-29 00:13:43,856 - src.simple_pipeline - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-29 00:13:43,857 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-29 00:13:44,119 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:13:44,120 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field name: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99634, Requested 1716. Please try again in 19m25.94s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:13:44,120 - src.plant_details_extractor - INFO - Extracted name: None (confidence: 0.00)
2025-05-29 00:13:44,706 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:13:44,707 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_type: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99633, Requested 1737. Please try again in 19m43.486s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:13:44,708 - src.plant_details_extractor - INFO - Extracted plant_type: None (confidence: 0.00)
2025-05-29 00:13:45,304 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:13:45,306 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_address: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99633, Requested 1730. Please try again in 19m36.848s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:13:45,306 - src.plant_details_extractor - INFO - Extracted plant_address: None (confidence: 0.00)
2025-05-29 00:13:45,878 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:13:45,879 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field lat: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99632, Requested 1736. Please try again in 19m41.453s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:13:45,879 - src.plant_details_extractor - INFO - Extracted lat: None (confidence: 0.00)
2025-05-29 00:13:46,492 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:13:46,493 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field long: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99631, Requested 1737. Please try again in 19m41.701s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:13:46,493 - src.plant_details_extractor - INFO - Extracted long: None (confidence: 0.00)
2025-05-29 00:13:47,052 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:13:47,053 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99631, Requested 1726. Please try again in 19m31.635s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:13:47,054 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-29 00:13:47,554 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-29 00:13:47,555 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "capacity"'
2025-05-29 00:13:47,555 - src.plant_details_extractor - INFO - Using organization name for plant name: CLP India Private Limited
2025-05-29 00:13:47,556 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-29 00:13:47,556 - src.simple_pipeline - INFO - ✅ Field 'name' extracted from cache
2025-05-29 00:13:47,556 - src.simple_pipeline - INFO - ✅ Field 'plant_id' extracted from cache
2025-05-29 00:13:47,556 - src.simple_pipeline - INFO - 🔍 Field 'lat' is missing - will search specifically
2025-05-29 00:13:47,557 - src.simple_pipeline - INFO - 🔍 Field 'long' is missing - will search specifically
2025-05-29 00:13:47,557 - src.simple_pipeline - INFO - 🔍 Field 'plant_address' is missing - will search specifically
2025-05-29 00:13:47,557 - src.simple_pipeline - INFO - 🔍 Field 'grid_connectivity_maps' is missing - will search specifically
2025-05-29 00:13:47,557 - src.simple_pipeline - INFO - 🔍 Field 'ppa_details' is missing - will search specifically
2025-05-29 00:13:47,557 - src.simple_pipeline - INFO - 🔍 Field 'plant_type' is missing - will search specifically
2025-05-29 00:13:47,557 - src.simple_pipeline - INFO - 🔍 Field 'units_id' is missing - will search specifically
2025-05-29 00:13:47,558 - src.simple_pipeline - INFO - 🎯 Starting targeted searches for 7 missing fields
2025-05-29 00:13:47,558 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'lat' data
2025-05-29 00:13:47,558 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant latitude coordinates GPS location'
2025-05-29 00:14:38,282 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:14:43,005 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:14:44,007 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-29 00:14:44,604 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-29 00:14:45,606 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-29 00:14:46,454 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-29 00:14:47,457 - src.simple_pipeline - INFO - ✅ Found targeted content for 'lat' - 3 sources
2025-05-29 00:14:47,458 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'lat' using RAG: 28.607111
2025-05-29 00:14:47,458 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'long' data
2025-05-29 00:14:47,458 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant longitude coordinates GPS location'
2025-05-29 00:15:09,532 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:15:10,577 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:15:11,578 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-29 00:15:12,834 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-29 00:15:13,836 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-29 00:15:14,643 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-29 00:15:15,645 - src.simple_pipeline - INFO - ✅ Found targeted content for 'long' - 3 sources
2025-05-29 00:15:15,645 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'long' using RAG: 76.6565
2025-05-29 00:15:15,645 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'plant_address' data
2025-05-29 00:15:15,645 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant complete address location street city'
2025-05-29 00:15:56,812 - src.simple_pipeline - INFO - Scraping 1/2: https://www.bloomberg.com/profile/company/0808736D:IN
2025-05-29 00:16:02,501 - src.simple_pipeline - INFO - Successfully scraped https://www.bloomberg.com/profile/company/0808736D:IN (193 chars)
2025-05-29 00:16:03,503 - src.simple_pipeline - INFO - Scraping 2/2: https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html
2025-05-29 00:16:03,739 - src.scraper_client - WARNING - Failed to scrape https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html: HTTP 403
2025-05-29 00:16:03,740 - src.simple_pipeline - WARNING - Content too short or failed for https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html
2025-05-29 00:16:04,743 - src.simple_pipeline - INFO - ✅ Found targeted content for 'plant_address' - 1 sources
2025-05-29 00:16:04,744 - src.simple_pipeline - INFO - 📝 Content found for 'plant_address' but RAG extraction failed
2025-05-29 00:16:04,745 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-29 00:16:04,745 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant grid connection substation transmission line'
2025-05-29 00:17:01,471 - src.serp_client - ERROR - SERP API request failed: 500, message='Internal Server Error', url=URL('https://api.scraperapi.com/structured/google/search?api_key=********************************&query=Jhajjar+Power+Plant+grid+connection+substation+transmission+line&num=3&page=1')
2025-05-29 00:17:20,729 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-29 00:17:29,682 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-29 00:17:29,684 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-29 00:17:29,684 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-29 00:17:30,685 - src.simple_pipeline - INFO - Scraping 2/3: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-29 00:17:50,109 - src.pdf_processor - INFO - Successfully extracted 112384 chars from PDF using pdfplumber: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-29 00:17:50,112 - src.scraper_client - INFO - Successfully processed PDF: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars, score: 1.00)
2025-05-29 00:17:50,112 - src.simple_pipeline - INFO - Successfully scraped http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars)
2025-05-29 00:17:51,113 - src.simple_pipeline - INFO - Scraping 3/3: https://www.indigrid.co.in/portfolio-assets/jhajjar-kt-transco-pvt-ltd-jktpl/
2025-05-29 00:17:57,660 - src.simple_pipeline - INFO - Successfully scraped https://www.indigrid.co.in/portfolio-assets/jhajjar-kt-transco-pvt-ltd-jktpl/ (846 chars)
2025-05-29 00:17:58,662 - src.simple_pipeline - INFO - ✅ Found targeted content for 'grid_connectivity_maps' - 3 sources
2025-05-29 00:17:58,664 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'grid_connectivity_maps' using RAG: [{'description': 'Grid connectivity details for Jhajjar Power Plant', 'details': [{'substation_name': 'Sonipat Substation', 'substation_type': 'transmission', 'capacity': '400 kV', 'latitude': '', 'longitude': '', 'description': 'Primary transmission connection point - approximately 70 km northeast', 'projects': [{'description': '400 kV transmission line from Jhajjar to Sonipat', 'distance': '70 km'}]}, {'substation_name': 'Mahendragarh Substation', 'substation_type': 'transmission', 'capacity': '400 kV', 'latitude': '', 'longitude': '', 'description': 'Secondary transmission connection point - approximately 50 km southwest', 'projects': [{'description': '400 kV transmission line from Jhajjar to Mahendragarh', 'distance': '50 km'}]}]}]
2025-05-29 00:17:58,665 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'ppa_details' data
2025-05-29 00:17:58,665 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant power purchase agreement PPA contract details'
2025-05-29 00:18:11,576 - src.simple_pipeline - INFO - Scraping 1/3: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-29 00:18:19,943 - src.pdf_processor - INFO - Successfully extracted 36539 chars from PDF using pdfplumber: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-29 00:18:19,944 - src.scraper_client - INFO - Successfully processed PDF: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars, score: 1.00)
2025-05-29 00:18:19,944 - src.simple_pipeline - INFO - Successfully scraped https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars)
2025-05-29 00:18:20,946 - src.simple_pipeline - INFO - Scraping 2/3: https://cercind.gov.in/2022/orders/363-MP-2019.pdf
2025-05-29 00:18:25,632 - src.pdf_processor - INFO - Successfully extracted 17990 chars from PDF using pdfplumber: https://cercind.gov.in/2022/orders/363-MP-2019.pdf
2025-05-29 00:18:25,633 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2022/orders/363-MP-2019.pdf (17981 chars, score: 1.00)
2025-05-29 00:18:25,633 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2022/orders/363-MP-2019.pdf (17981 chars)
2025-05-29 00:18:26,635 - src.simple_pipeline - INFO - Scraping 3/3: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-29 00:18:38,367 - src.pdf_processor - INFO - Successfully extracted 111606 chars from PDF using pdfplumber: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-29 00:18:38,368 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars, score: 1.00)
2025-05-29 00:18:38,368 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars)
2025-05-29 00:18:39,370 - src.simple_pipeline - INFO - ✅ Found targeted content for 'ppa_details' - 3 sources
2025-05-29 00:18:39,373 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'ppa_details' using RAG: [{'description': 'Power Purchase Agreement for Jhajjar Power Plant', 'capacity': '1320 MW', 'capacity_unit': 'MW', 'start_date': '2012', 'end_date': '', 'tenure': 30, 'tenure_type': 'Years', 'respondents': [{'name': 'Haryana State Distribution Companies', 'capacity': '1188 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}, {'name': 'External Power Buyers', 'capacity': '132 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}]}]
2025-05-29 00:18:39,374 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'plant_type' data
2025-05-29 00:18:39,374 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant power plant type coal gas nuclear solar wind'
2025-05-29 00:18:53,751 - src.simple_pipeline - INFO - Scraping 1/3: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-29 00:18:58,914 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-29 00:18:59,915 - src.simple_pipeline - INFO - Scraping 2/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:19:01,177 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:19:02,178 - src.simple_pipeline - INFO - Scraping 3/3: https://apraava.com/projects/jhajjar-power-plant
2025-05-29 00:19:04,295 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-29 00:19:05,297 - src.simple_pipeline - INFO - ✅ Found targeted content for 'plant_type' - 3 sources
2025-05-29 00:19:05,298 - src.simple_pipeline - INFO - 📝 Content found for 'plant_type' but RAG extraction failed
2025-05-29 00:19:05,298 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'units_id' data
2025-05-29 00:19:05,298 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant units generators unit numbers'
2025-05-29 00:19:09,216 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-29 00:19:26,335 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-29 00:19:26,337 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-29 00:19:26,337 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-29 00:19:27,338 - src.simple_pipeline - INFO - Scraping 2/3: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf
2025-05-29 00:19:37,431 - src.pdf_processor - INFO - Successfully extracted 89455 chars from PDF using pdfplumber: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf
2025-05-29 00:19:37,433 - src.scraper_client - INFO - Successfully processed PDF: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf (50003 chars, score: 1.00)
2025-05-29 00:19:37,433 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf (50003 chars)
2025-05-29 00:19:38,434 - src.simple_pipeline - INFO - Scraping 3/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:19:39,484 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:19:40,486 - src.simple_pipeline - INFO - ✅ Found targeted content for 'units_id' - 3 sources
2025-05-29 00:19:40,495 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'units_id' using RAG: ['Unit 1', 'Unit 3', 'Unit 250']
2025-05-29 00:19:40,502 - src.simple_pipeline - INFO - 💾 Targeted content saved to jhajjar_power_plant_targeted_content_20250529_001940.json
2025-05-29 00:19:40,503 - src.simple_pipeline - ERROR - Error creating PlantDetails object: 3 validation errors for PlantDetails
units_id.0
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
units_id.1
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 3', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
units_id.2
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 250', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing
2025-05-29 00:19:40,503 - src.simple_pipeline - INFO - 🎉 Simplified extraction completed for: Jhajjar Power Plant
2025-05-29 00:19:40,503 - src.simple_pipeline - INFO - ⏱️  Total time: 392.4s
2025-05-29 00:19:40,503 - src.simple_pipeline - INFO - 💾 Cache efficiency: 2 fields from cache, 7 targeted searches
2025-05-29 00:19:40,503 - src.vector_rag_pipeline - INFO - ✅ Vector RAG enhanced extraction completed in 392.38s
2025-05-29 00:19:40,516 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with Groq
2025-05-29 00:19:40,538 - src.plant_details_extractor - INFO - Plant details extractor initialized with Groq
2025-05-29 00:19:40,538 - src.vector_rag.pipeline_integration - INFO - ✅ Vector RAG integration enabled
2025-05-29 00:19:40,538 - src.vector_rag_pipeline - INFO - Vector RAG Enhanced Pipeline initialized (RAG enabled: True)
2025-05-29 00:19:40,538 - src.vector_rag_pipeline - INFO - 🚀 Starting Vector RAG enhanced extraction for: Jhajjar Power Plant
2025-05-29 00:19:40,538 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-29 00:19:40,538 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-29 00:19:45,575 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-29 00:19:45,576 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-29 00:19:51,598 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-29 00:19:52,600 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:19:57,405 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:19:58,407 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-29 00:20:00,289 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-29 00:20:01,291 - src.simple_pipeline - INFO - Scraping 4/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-29 00:20:02,947 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-29 00:20:03,949 - src.simple_pipeline - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-29 00:20:05,509 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-29 00:20:06,512 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-29 00:20:06,512 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-29 00:20:06,513 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['wikipedia', 'other', 'company_official']}
2025-05-29 00:20:06,513 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-29 00:20:06,513 - src.groq_client - INFO - Starting LLM extraction for 5 content pieces
2025-05-29 00:20:06,874 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:20:06,876 - src.groq_client - ERROR - Groq extraction failed for field organization_name: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99191, Requested 1168. Please try again in 5m9.714s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:20:06,939 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:20:06,941 - src.groq_client - ERROR - Groq extraction failed for field cfpp_type: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99191, Requested 1246. Please try again in 6m17.034999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:20:06,941 - src.groq_client - INFO - Extracted cfpp_type: None (confidence: 0.00)
2025-05-29 00:20:07,510 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:20:07,512 - src.groq_client - ERROR - Groq extraction failed for field country_name: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99190, Requested 1134. Please try again in 4m39.696999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:20:07,513 - src.groq_client - INFO - Extracted country_name: None (confidence: 0.00)
2025-05-29 00:20:08,089 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:20:08,091 - src.groq_client - ERROR - Groq extraction failed for field province: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99190, Requested 1152. Please try again in 4m54.67s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:20:08,091 - src.groq_client - INFO - Extracted province: None (confidence: 0.00)
2025-05-29 00:20:08,666 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:20:08,667 - src.groq_client - ERROR - Groq extraction failed for field plants_count: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99189, Requested 1134. Please try again in 4m38.546999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:20:08,668 - src.groq_client - INFO - Extracted plants_count: None (confidence: 0.00)
2025-05-29 00:20:09,242 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:20:09,244 - src.groq_client - ERROR - Groq extraction failed for field plant_types: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99188, Requested 1153. Please try again in 4m54.39s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:20:09,244 - src.groq_client - INFO - Extracted plant_types: None (confidence: 0.00)
2025-05-29 00:20:09,873 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:20:09,875 - src.groq_client - ERROR - Groq extraction failed for field ppa_flag: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99188, Requested 1206. Please try again in 5m39.554999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:20:09,875 - src.groq_client - INFO - Extracted ppa_flag: None (confidence: 0.00)
2025-05-29 00:20:10,465 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:20:10,466 - src.groq_client - ERROR - Groq extraction failed for field currency_in: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99187, Requested 1158. Please try again in 4m57.485s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:20:10,467 - src.groq_client - INFO - Extracted currency_in: None (confidence: 0.00)
2025-05-29 00:20:11,122 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:20:11,124 - src.groq_client - ERROR - Groq extraction failed for field financial_year: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99186, Requested 1278. Please try again in 6m40.505s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:20:11,125 - src.groq_client - INFO - Extracted financial_year: None (confidence: 0.00)
2025-05-29 00:20:11,626 - src.groq_client - INFO - LLM extraction completed
2025-05-29 00:20:11,627 - src.simple_pipeline - INFO - ✅ Organizational extraction completed
2025-05-29 00:20:11,627 - src.simple_pipeline - INFO - 🔧 Step 4: Extracting plant details with smart field-by-field approach
2025-05-29 00:20:11,627 - src.simple_pipeline - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-29 00:20:11,628 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-29 00:20:11,733 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:20:11,734 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field name: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99185, Requested 1716. Please try again in 12m58.326s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:20:11,734 - src.plant_details_extractor - INFO - Extracted name: None (confidence: 0.00)
2025-05-29 00:20:12,307 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:20:12,309 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_type: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99185, Requested 1737. Please try again in 13m15.896s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:20:12,309 - src.plant_details_extractor - INFO - Extracted plant_type: None (confidence: 0.00)
2025-05-29 00:20:12,939 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:20:12,941 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_address: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99184, Requested 1730. Please try again in 13m9.214s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:20:12,942 - src.plant_details_extractor - INFO - Extracted plant_address: None (confidence: 0.00)
2025-05-29 00:20:13,513 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:20:13,513 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field lat: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99183, Requested 1736. Please try again in 13m13.822s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:20:13,513 - src.plant_details_extractor - INFO - Extracted lat: None (confidence: 0.00)
2025-05-29 00:20:14,086 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:20:14,087 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field long: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99183, Requested 1737. Please try again in 13m14.114999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:20:14,088 - src.plant_details_extractor - INFO - Extracted long: None (confidence: 0.00)
2025-05-29 00:20:14,663 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-29 00:20:14,665 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: Error code: 429 - {'error': {'message': 'Rate limit reached for model `llama-3.3-70b-versatile` in organization `org_01jg919wzxfk6bgv2gng6mt0c1` service tier `on_demand` on tokens per day (TPD): Limit 100000, Used 99182, Requested 1726. Please try again in 13m4.037s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-05-29 00:20:14,665 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-29 00:20:15,167 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-29 00:20:15,168 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "capacity"'
2025-05-29 00:20:15,168 - src.plant_details_extractor - INFO - Using input plant name as fallback: Jhajjar Power Plant
2025-05-29 00:20:15,168 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-29 00:20:15,168 - src.simple_pipeline - INFO - ✅ Field 'name' extracted from cache
2025-05-29 00:20:15,168 - src.simple_pipeline - INFO - ✅ Field 'plant_id' extracted from cache
2025-05-29 00:20:15,169 - src.simple_pipeline - INFO - 🔍 Field 'lat' is missing - will search specifically
2025-05-29 00:20:15,169 - src.simple_pipeline - INFO - 🔍 Field 'long' is missing - will search specifically
2025-05-29 00:20:15,169 - src.simple_pipeline - INFO - 🔍 Field 'plant_address' is missing - will search specifically
2025-05-29 00:20:15,169 - src.simple_pipeline - INFO - 🔍 Field 'grid_connectivity_maps' is missing - will search specifically
2025-05-29 00:20:15,169 - src.simple_pipeline - INFO - 🔍 Field 'ppa_details' is missing - will search specifically
2025-05-29 00:20:15,169 - src.simple_pipeline - INFO - 🔍 Field 'plant_type' is missing - will search specifically
2025-05-29 00:20:15,169 - src.simple_pipeline - INFO - 🔍 Field 'units_id' is missing - will search specifically
2025-05-29 00:20:15,169 - src.simple_pipeline - INFO - 🎯 Starting targeted searches for 7 missing fields
2025-05-29 00:20:15,169 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'lat' data
2025-05-29 00:20:15,170 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant latitude coordinates GPS location'
2025-05-29 00:20:34,032 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-29 00:20:35,009 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-29 00:20:36,010 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
