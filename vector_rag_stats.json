{"vector_rag_stats": {"attempted_fields": ["plant_address"], "successful_extractions": [], "failed_extractions": [], "vector_db_created": true, "targeted_content_file": "jhajjar_power_plant_targeted_content_20250529_003159.json", "error": "3 validation errors for PlantDetails\nunits_id.0\n  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 1', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing\nunits_id.1\n  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 3', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing\nunits_id.2\n  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 250', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.5/v/int_parsing"}, "rag_system_info": {"enabled": true, "vector_db_path": "vector_db", "embedding_model": "all-MiniLM-L6-v2"}, "timestamp": "2025-05-29T00:31:59.947885"}